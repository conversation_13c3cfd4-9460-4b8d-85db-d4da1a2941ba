# IAM configuration for dev environment

include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../terraform/iam"
}

# Get current AWS account ID
locals {
  account_id = get_aws_account_id()
  region     = "us-east-1"
}

inputs = {
  environment    = "dev"
  project_name   = "entanglement"
  aws_region     = local.region
  aws_account_id = local.account_id

  # EC2 instances for start/stop operations
  ec2_instance_arns = [
    "arn:aws:ec2:${local.region}:${local.account_id}:instance/i-0456aed95c9d1bd48",
    "arn:aws:ec2:${local.region}:${local.account_id}:instance/i-026c9c5455845cdb0"
  ]

  # Lambda function ARNs
  lambda_function_arns = {
    start_ec2 = "arn:aws:lambda:${local.region}:${local.account_id}:function:StartInstancEC2"
    stop_ec2  = "arn:aws:lambda:${local.region}:${local.account_id}:function:StopInstanceEC2"
  }

  # Common tags
  tags = {
    Environment = "dev"
    Project     = "entanglement"
    Owner       = "DevOps Team"
    CostCenter  = "Engineering"
    Terraform   = "true"
  }
}
