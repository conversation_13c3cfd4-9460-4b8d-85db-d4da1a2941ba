{"version": 4, "terraform_version": "1.12.1", "serial": 21, "lineage": "054452b1-3dd6-a160-f94d-99d63d3766e5", "outputs": {"cognito_publish_policy_arn": {"value": "arn:aws:iam::************:policy/dev-entanglement-cognito-publish-policy", "type": "string"}, "cognito_service_role_arn": {"value": "arn:aws:iam::************:role/dev-entanglement-cognito-service-role", "type": "string"}, "cognito_service_role_name": {"value": "dev-entanglement-cognito-service-role", "type": "string"}, "ec2_application_role_arn": {"value": "arn:aws:iam::************:role/dev-entanglement-ec2-application-role", "type": "string"}, "ec2_application_role_name": {"value": "dev-entanglement-ec2-application-role", "type": "string"}, "ec2_instance_profile_arn": {"value": "arn:aws:iam::************:instance-profile/dev-entanglement-ec2-application-profile", "type": "string"}, "ec2_instance_profile_name": {"value": "dev-entanglement-ec2-application-profile", "type": "string"}, "ec2_start_stop_policy_arn": {"value": "arn:aws:iam::************:policy/dev-entanglement-start-stop-ec2-instance-policy", "type": "string"}, "ecr_access_policy_arn": {"value": "arn:aws:iam::************:policy/dev-entanglement-ecr-access-policy", "type": "string"}, "eventbridge_scheduler_role_arn": {"value": "arn:aws:iam::************:role/dev-entanglement-eventbridge-scheduler-role", "type": "string"}, "eventbridge_scheduler_role_name": {"value": "dev-entanglement-eventbridge-scheduler-role", "type": "string"}, "eventbridge_start_ec2_policy_arn": {"value": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "type": "string"}, "eventbridge_stop_ec2_policy_arn": {"value": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "type": "string"}, "iam_resources_summary": {"value": {"instance_profiles": ["dev-entanglement-ec2-application-profile"], "policies": ["dev-entanglement-start-stop-ec2-instance-policy", "dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "dev-entanglement-cognito-publish-policy", "dev-entanglement-ecr-access-policy", "dev-entanglement-kvs-access-policy"], "policies_count": 6, "roles": ["dev-entanglement-lambda-ec2-management-role", "dev-entanglement-eventbridge-scheduler-role", "dev-entanglement-cognito-service-role", "dev-entanglement-ec2-application-role"], "roles_count": 4}, "type": ["object", {"instance_profiles": ["tuple", ["string"]], "policies": ["tuple", ["string", "string", "string", "string", "string", "string"]], "policies_count": "number", "roles": ["tuple", ["string", "string", "string", "string"]], "roles_count": "number"}]}, "kvs_access_policy_arn": {"value": "arn:aws:iam::************:policy/dev-entanglement-kvs-access-policy", "type": "string"}, "lambda_ec2_management_role_arn": {"value": "arn:aws:iam::************:role/dev-entanglement-lambda-ec2-management-role", "type": "string"}, "lambda_ec2_management_role_name": {"value": "dev-entanglement-lambda-ec2-management-role", "type": "string"}, "policy_arns": {"value": {"cognito_publish": "arn:aws:iam::************:policy/dev-entanglement-cognito-publish-policy", "ec2_start_stop": "arn:aws:iam::************:policy/dev-entanglement-start-stop-ec2-instance-policy", "ecr_access": "arn:aws:iam::************:policy/dev-entanglement-ecr-access-policy", "eventbridge_start_ec2": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "eventbridge_stop_ec2": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "kvs_access": "arn:aws:iam::************:policy/dev-entanglement-kvs-access-policy"}, "type": ["object", {"cognito_publish": "string", "ec2_start_stop": "string", "ecr_access": "string", "eventbridge_start_ec2": "string", "eventbridge_stop_ec2": "string", "kvs_access": "string"}]}, "role_arns": {"value": {"cognito_service": "arn:aws:iam::************:role/dev-entanglement-cognito-service-role", "ec2_application": "arn:aws:iam::************:role/dev-entanglement-ec2-application-role", "eventbridge_scheduler": "arn:aws:iam::************:role/dev-entanglement-eventbridge-scheduler-role", "lambda_ec2_management": "arn:aws:iam::************:role/dev-entanglement-lambda-ec2-management-role"}, "type": ["object", {"cognito_service": "string", "ec2_application": "string", "eventbridge_scheduler": "string", "lambda_ec2_management": "string"}]}}, "resources": [{"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/<EMAIL>", "id": "************", "user_id": "AIDAUSJEUSJY4RSKL7JDP"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Asia Pacific (Tokyo)", "endpoint": "ec2.ap-northeast-1.amazonaws.com", "id": "ap-northeast-1", "name": "ap-northeast-1", "region": "ap-northeast-1"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "aws_iam_instance_profile", "name": "ec2_application", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:instance-profile/dev-entanglement-ec2-application-profile", "create_date": "2025-07-04T07:22:33Z", "id": "dev-entanglement-ec2-application-profile", "name": "dev-entanglement-ec2-application-profile", "name_prefix": "", "path": "/", "role": "dev-entanglement-ec2-application-role", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-ec2-application-profile", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "InstanceProfile"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-ec2-application-profile", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "InstanceProfile"}, "unique_id": "AIPAUSJEUSJYQGMUFTVAY"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.ec2_application"]}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "cognito_publish", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/dev-entanglement-cognito-publish-policy", "attachment_count": 0, "description": "Policy to allow publishing to SNS topics for Cognito", "id": "arn:aws:iam::************:policy/dev-entanglement-cognito-publish-policy", "name": "dev-entanglement-cognito-publish-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"sns:publish\"],\"Effect\":\"Allow\",\"Resource\":[\"*\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAUSJEUSJY4HIMY2GWM", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-cognito-publish-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "CognitoSNS"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-cognito-publish-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "CognitoSNS"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "ec2_start_stop", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/dev-entanglement-start-stop-ec2-instance-policy", "attachment_count": 0, "description": "Policy to allow starting and stopping specific EC2 instances", "id": "arn:aws:iam::************:policy/dev-entanglement-start-stop-ec2-instance-policy", "name": "dev-entanglement-start-stop-ec2-instance-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"ec2:StartInstances\",\"ec2:StopInstances\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:ec2:us-east-1:************:instance/i-0456aed95c9d1bd48\",\"arn:aws:ec2:us-east-1:************:instance/i-026c9c5455845cdb0\"],\"Sid\":\"VisualEditor0\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAUSJEUSJYVMKVS26S6", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-start-stop-ec2-instance-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EC2Management"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-start-stop-ec2-instance-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EC2Management"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "ecr_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/dev-entanglement-ecr-access-policy", "attachment_count": 0, "description": "Policy to allow access to ECR repositories", "id": "arn:aws:iam::************:policy/dev-entanglement-ecr-access-policy", "name": "dev-entanglement-ecr-access-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"ecr:GetDownloadUrlForLayer\",\"ecr:BatchGetImage\",\"ecr:BatchCheckLayerAvailability\",\"ecr:GetAuthorizationToken\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAUSJEUSJYSWOQLHYDE", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-ecr-access-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "ECRAccess"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-ecr-access-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "ECRAccess"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "eventbridge_start_ec2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "attachment_count": 0, "description": "Policy for EventBridge to invoke Lambda function for starting EC2 instances", "id": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "name": "dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"lambda:InvokeFunction\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:lambda:us-east-1:************:function:StartInstancEC2:*\",\"arn:aws:lambda:us-east-1:************:function:StartInstancEC2\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAUSJEUSJYVOBB2RUEW", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-eventbridge-start-ec2-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EventBridgeExecution"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-eventbridge-start-ec2-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EventBridgeExecution"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "eventbridge_stop_ec2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "attachment_count": 0, "description": "Policy for EventBridge to invoke Lambda function for stopping EC2 instances", "id": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "name": "dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"lambda:InvokeFunction\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:lambda:us-east-1:************:function:StopInstanceEC2:*\",\"arn:aws:lambda:us-east-1:************:function:StopInstanceEC2\"]}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAUSJEUSJY77NSKYROR", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-eventbridge-stop-ec2-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EventBridgeExecution"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-eventbridge-stop-ec2-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EventBridgeExecution"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "kvs_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/dev-entanglement-kvs-access-policy", "attachment_count": 0, "description": "Policy to allow access to Kinesis Video Streams", "id": "arn:aws:iam::************:policy/dev-entanglement-kvs-access-policy", "name": "dev-entanglement-kvs-access-policy", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"kinesisvideo:*\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPAUSJEUSJYS35APZOC3", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-kvs-access-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "KinesisVideoStreams"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-kvs-access-policy", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "KinesisVideoStreams"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "cognito_service", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/dev-entanglement-cognito-service-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"cognito-idp.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-04T07:22:32Z", "description": "", "force_detach_policies": false, "id": "dev-entanglement-cognito-service-role", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "dev-entanglement-cognito-service-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-cognito-service-role", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "CognitoServiceRole"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-cognito-service-role", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "CognitoServiceRole"}, "unique_id": "AROAUSJEUSJY25JIVC5AE"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "name": "dev-entanglement-cognito-service-role"}, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "ec2_application", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/dev-entanglement-ec2-application-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-04T07:22:31Z", "description": "", "force_detach_policies": false, "id": "dev-entanglement-ec2-application-role", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "dev-entanglement-ec2-application-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-ec2-application-role", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EC2InstanceRole"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-ec2-application-role", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EC2InstanceRole"}, "unique_id": "AROAUSJEUSJYQC2A5ZKZR"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "name": "dev-entanglement-ec2-application-role"}, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eventbridge_scheduler", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/dev-entanglement-eventbridge-scheduler-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"scheduler.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-04T07:22:32Z", "description": "", "force_detach_policies": false, "id": "dev-entanglement-eventbridge-scheduler-role", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "dev-entanglement-eventbridge-scheduler-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-eventbridge-scheduler-role", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EventBridgeSchedulerRole"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-eventbridge-scheduler-role", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "EventBridgeSchedulerRole"}, "unique_id": "AROAUSJEUSJYUIPDHHJUX"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "name": "dev-entanglement-eventbridge-scheduler-role"}, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "lambda_ec2_management", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/dev-entanglement-lambda-ec2-management-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-07-04T07:22:32Z", "description": "", "force_detach_policies": false, "id": "dev-entanglement-lambda-ec2-management-role", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "dev-entanglement-lambda-ec2-management-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-lambda-ec2-management-role", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "LambdaExecutionRole"}, "tags_all": {"CostCenter": "Engineering", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-lambda-ec2-management-role", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "LambdaExecutionRole"}, "unique_id": "AROAUSJEUSJYVCDFLO5TN"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "name": "dev-entanglement-lambda-ec2-management-role"}, "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "cognito_sns_publish", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-cognito-service-role/arn:aws:iam::************:policy/dev-entanglement-cognito-publish-policy", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-cognito-publish-policy", "role": "dev-entanglement-cognito-service-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-cognito-publish-policy", "role": "dev-entanglement-cognito-service-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_policy.cognito_publish", "aws_iam_role.cognito_service"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ec2_cloudwatch_agent", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-ec2-application-role/arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy", "policy_arn": "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy", "role": "dev-entanglement-ec2-application-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy", "role": "dev-entanglement-ec2-application-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_role.ec2_application"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ec2_ecr_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-ec2-application-role/arn:aws:iam::************:policy/dev-entanglement-ecr-access-policy", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-ecr-access-policy", "role": "dev-entanglement-ec2-application-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-ecr-access-policy", "role": "dev-entanglement-ec2-application-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_policy.ecr_access", "aws_iam_role.ec2_application"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ec2_kvs_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-ec2-application-role/arn:aws:iam::************:policy/dev-entanglement-kvs-access-policy", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-kvs-access-policy", "role": "dev-entanglement-ec2-application-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-kvs-access-policy", "role": "dev-entanglement-ec2-application-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_policy.kvs_access", "aws_iam_role.ec2_application"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ec2_ssm_managed_instance", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-ec2-application-role/arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore", "policy_arn": "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore", "role": "dev-entanglement-ec2-application-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore", "role": "dev-entanglement-ec2-application-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_role.ec2_application"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eventbridge_start_ec2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-eventbridge-scheduler-role/arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "role": "dev-entanglement-eventbridge-scheduler-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-start-ec2-instance", "role": "dev-entanglement-eventbridge-scheduler-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_policy.eventbridge_start_ec2", "aws_iam_role.eventbridge_scheduler"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eventbridge_stop_ec2", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-eventbridge-scheduler-role/arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "role": "dev-entanglement-eventbridge-scheduler-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-eventbridge-scheduler-execution-policy-stop-ec2-instance", "role": "dev-entanglement-eventbridge-scheduler-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_policy.eventbridge_stop_ec2", "aws_iam_role.eventbridge_scheduler"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "lambda_ec2_basic_execution", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-lambda-ec2-management-role/arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "role": "dev-entanglement-lambda-ec2-management-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "role": "dev-entanglement-lambda-ec2-management-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_role.lambda_ec2_management"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "lambda_ec2_start_stop", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "dev-entanglement-lambda-ec2-management-role/arn:aws:iam::************:policy/dev-entanglement-start-stop-ec2-instance-policy", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-start-stop-ec2-instance-policy", "role": "dev-entanglement-lambda-ec2-management-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "identity": {"account_id": "************", "policy_arn": "arn:aws:iam::************:policy/dev-entanglement-start-stop-ec2-instance-policy", "role": "dev-entanglement-lambda-ec2-management-role"}, "private": "bnVsbA==", "dependencies": ["aws_iam_policy.ec2_start_stop", "aws_iam_role.lambda_ec2_management"]}]}], "check_results": null}