# IAM Module

This module creates IAM policies, roles, and instance profiles for managing AWS resources including EC2 instances, Lambda functions, EventBridge, Cognito, ECR, and Kinesis Video Streams.

## Resources Created

### IAM Policies (6)
1. **EC2 Start/Stop Policy** - Allows starting and stopping specific EC2 instances
2. **EventBridge Start EC2 Policy** - Allows EventBridge to invoke Lambda function for starting EC2
3. **EventBridge Stop EC2 Policy** - Allows EventBridge to invoke Lambda function for stopping EC2
4. **Cognito Publish Policy** - Allows publishing to SNS topics for Cognito
5. **ECR Access Policy** - Allows access to ECR repositories
6. **KVS Access Policy** - Allows access to Kinesis Video Streams

### IAM Roles (4)
1. **Lambda EC2 Management Role** - For Lambda functions managing EC2 instances
2. **EventBridge Scheduler Role** - For EventBridge scheduler service
3. **Cognito Service Role** - For Cognito identity provider service
4. **EC2 Application Role** - For applications running on EC2 instances

### Instance Profiles (1)
1. **EC2 Application Profile** - Instance profile for EC2 instances

## Usage

### Basic Usage
```hcl
module "iam" {
  source = "./terraform/iam"
  
  environment    = "dev"
  project_name   = "entanglement"
  aws_region     = "us-east-1"
  aws_account_id = "************"
  
  ec2_instance_arns = [
    "arn:aws:ec2:us-east-1:************:instance/i-1234567890abcdef0"
  ]
  
  lambda_function_arns = {
    start_ec2 = "arn:aws:lambda:us-east-1:************:function:StartEC2"
    stop_ec2  = "arn:aws:lambda:us-east-1:************:function:StopEC2"
  }
  
  tags = {
    Environment = "dev"
    Project     = "entanglement"
  }
}
```

### Terragrunt Usage
```hcl
include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../terraform/iam"
}

inputs = {
  environment    = "dev"
  project_name   = "entanglement"
  aws_region     = "us-east-1"
  aws_account_id = get_aws_account_id()
  
  ec2_instance_arns = [
    "arn:aws:ec2:us-east-1:${get_aws_account_id()}:instance/i-0456aed95c9d1bd48"
  ]
  
  lambda_function_arns = {
    start_ec2 = "arn:aws:lambda:us-east-1:${get_aws_account_id()}:function:StartInstancEC2"
    stop_ec2  = "arn:aws:lambda:us-east-1:${get_aws_account_id()}:function:StopInstanceEC2"
  }
}
```

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| environment | Environment name (dev, staging, prod) | `string` | `"dev"` | no |
| project_name | Name of the project | `string` | `"entanglement"` | no |
| aws_region | AWS region | `string` | `"us-east-1"` | no |
| aws_account_id | AWS account ID | `string` | n/a | yes |
| ec2_instance_arns | List of EC2 instance ARNs for start/stop operations | `list(string)` | `[...]` | no |
| lambda_function_arns | Map of Lambda function ARNs | `object({...})` | `{...}` | no |
| tags | Common tags to apply to all resources | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| policy_arns | Map of all policy ARNs created by this module |
| role_arns | Map of all role ARNs created by this module |
| lambda_ec2_management_role_arn | ARN of the Lambda EC2 management role |
| eventbridge_scheduler_role_arn | ARN of the EventBridge scheduler role |
| cognito_service_role_arn | ARN of the Cognito service role |
| ec2_application_role_arn | ARN of the EC2 application role |
| ec2_instance_profile_arn | ARN of the EC2 instance profile |
| ec2_instance_profile_name | Name of the EC2 instance profile |
| iam_resources_summary | Summary of all IAM resources created |

## Policy Details

### EC2 Start/Stop Policy
- **Actions**: `ec2:StartInstances`, `ec2:StopInstances`
- **Resources**: Specific EC2 instance ARNs

### EventBridge Policies
- **Actions**: `lambda:InvokeFunction`
- **Resources**: Specific Lambda function ARNs

### Cognito Publish Policy
- **Actions**: `sns:publish`
- **Resources**: All SNS topics (`*`)

### ECR Access Policy
- **Actions**: `ecr:GetDownloadUrlForLayer`, `ecr:BatchGetImage`, `ecr:BatchCheckLayerAvailability`, `ecr:GetAuthorizationToken`
- **Resources**: All ECR repositories (`*`)

### KVS Access Policy
- **Actions**: `kinesisvideo:*`
- **Resources**: All Kinesis Video Streams (`*`)

## Security Considerations

1. **Principle of Least Privilege**: Policies are designed with minimal required permissions
2. **Resource-Specific**: EC2 and Lambda policies target specific resources
3. **Service-Specific Roles**: Each role is designed for a specific AWS service
4. **Tagging**: All resources are properly tagged for governance

## Examples

### Using the EC2 Instance Profile
```hcl
resource "aws_instance" "app" {
  ami                  = "ami-12345678"
  instance_type        = "t3.micro"
  iam_instance_profile = module.iam.ec2_instance_profile_name
  
  # This instance will have ECR and KVS access
}
```

### Using Lambda Role
```hcl
resource "aws_lambda_function" "ec2_manager" {
  function_name = "ec2-manager"
  role         = module.iam.lambda_ec2_management_role_arn
  
  # This Lambda can start/stop EC2 instances
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | >= 5.0 |

## Authors

Module managed by DevOps Team.

## License

Apache 2 Licensed. See LICENSE for full details.
