# VPC module main configuration

# Local values for common tags and naming
locals {
  common_tags = merge(var.tags, {
    Environment = var.environment
    Project     = var.project_name
    ManagedBy   = "Terraform"
  })

  name_prefix = "${var.environment}-${var.project_name}-vpc"
}

# Data sources
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

#==============================================================================
# VPC
#==============================================================================

resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support

  tags = merge(local.common_tags, {
    Name = local.name_prefix
    Type = "VPC"
  })
}

#==============================================================================
# INTERNET GATEWAY
#==============================================================================

resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-igw"
    Type = "InternetGateway"
  })
}

#==============================================================================
# PUBLIC SUBNETS
#==============================================================================

resource "aws_subnet" "public" {
  count = length(var.public_subnet_cidrs)

  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.public_subnet_cidrs[count.index]
  availability_zone       = var.availability_zones[count.index]
  map_public_ip_on_launch = true

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-subnet-public${count.index + 1}-${var.availability_zones[count.index]}"
    Type = "PublicSubnet"
    Tier = "Public"
  })
}

#==============================================================================
# PRIVATE APPLICATION SUBNETS
#==============================================================================

resource "aws_subnet" "private" {
  count = length(var.private_subnet_cidrs)

  vpc_id            = aws_vpc.main.id
  cidr_block        = var.private_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-subnet-private${count.index + 1}-${var.availability_zones[count.index]}"
    Type = "PrivateSubnet"
    Tier = "Private"
  })
}

#==============================================================================
# RDS PRIVATE SUBNETS
#==============================================================================

resource "aws_subnet" "rds" {
  count = length(var.rds_subnet_cidrs)

  vpc_id            = aws_vpc.main.id
  cidr_block        = var.rds_subnet_cidrs[count.index]
  availability_zone = var.availability_zones[count.index]

  tags = merge(local.common_tags, {
    Name = "RDS-Pvt-subnet-${count.index + 1}"
    Type = "DatabaseSubnet"
    Tier = "Database"
  })
}

#==============================================================================
# ELASTIC IP FOR NAT GATEWAY
#==============================================================================

resource "aws_eip" "nat" {
  count = var.enable_nat_gateway ? 1 : 0

  domain = "vpc"

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-nat-eip"
    Type = "ElasticIP"
  })

  depends_on = [aws_internet_gateway.main]
}

#==============================================================================
# NAT GATEWAY
#==============================================================================

resource "aws_nat_gateway" "main" {
  count = var.enable_nat_gateway ? 1 : 0

  allocation_id = aws_eip.nat[0].id
  subnet_id     = aws_subnet.public[0].id

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-nat-gateway"
    Type = "NATGateway"
  })

  depends_on = [aws_internet_gateway.main]
}

#==============================================================================
# ROUTE TABLES
#==============================================================================

# Public Route Table
resource "aws_route_table" "public" {
  vpc_id = aws_vpc.main.id

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-rtb-public"
    Type = "PublicRouteTable"
  })
}

# Private Route Tables (one per AZ for private application subnets)
resource "aws_route_table" "private" {
  count = length(var.private_subnet_cidrs)

  vpc_id = aws_vpc.main.id

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-rtb-private${count.index + 1}-${var.availability_zones[count.index]}"
    Type = "PrivateRouteTable"
  })
}

# RDS Route Table
resource "aws_route_table" "rds" {
  vpc_id = aws_vpc.main.id

  tags = merge(local.common_tags, {
    Name = "RDS-Pvt-rt"
    Type = "DatabaseRouteTable"
  })
}

# Custom Route Table (additional)
resource "aws_route_table" "custom" {
  vpc_id = aws_vpc.main.id

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-rtb-custom"
    Type = "CustomRouteTable"
  })
}

#==============================================================================
# ROUTES
#==============================================================================

# Public Route to Internet Gateway
resource "aws_route" "public_internet" {
  route_table_id         = aws_route_table.public.id
  destination_cidr_block = "0.0.0.0/0"
  gateway_id             = aws_internet_gateway.main.id
}

# Private Routes to NAT Gateway
resource "aws_route" "private_nat" {
  count = var.enable_nat_gateway ? length(var.private_subnet_cidrs) : 0

  route_table_id         = aws_route_table.private[count.index].id
  destination_cidr_block = "0.0.0.0/0"
  nat_gateway_id         = aws_nat_gateway.main[0].id
}

#==============================================================================
# ROUTE TABLE ASSOCIATIONS
#==============================================================================

# Public Subnet Associations
resource "aws_route_table_association" "public" {
  count = length(var.public_subnet_cidrs)

  subnet_id      = aws_subnet.public[count.index].id
  route_table_id = aws_route_table.public.id
}

# Private Subnet Associations
resource "aws_route_table_association" "private" {
  count = length(var.private_subnet_cidrs)

  subnet_id      = aws_subnet.private[count.index].id
  route_table_id = aws_route_table.private[count.index].id
}

# RDS Subnet Associations
resource "aws_route_table_association" "rds" {
  count = length(var.rds_subnet_cidrs)

  subnet_id      = aws_subnet.rds[count.index].id
  route_table_id = aws_route_table.rds.id
}

#==============================================================================
# DB SUBNET GROUP
#==============================================================================

resource "aws_db_subnet_group" "main" {
  name       = "${local.name_prefix}-db-subnet-group"
  subnet_ids = aws_subnet.rds[*].id

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-db-subnet-group"
    Type = "DBSubnetGroup"
  })
}

#==============================================================================
# VPC ENDPOINTS
#==============================================================================

# S3 VPC Endpoint (Gateway Endpoint)
resource "aws_vpc_endpoint" "s3" {
  count = var.enable_s3_endpoint ? 1 : 0

  vpc_id            = aws_vpc.main.id
  service_name      = "com.amazonaws.${data.aws_region.current.name}.s3"
  vpc_endpoint_type = "Gateway"

  route_table_ids = concat(
    [aws_route_table.public.id],
    aws_route_table.private[*].id,
    [aws_route_table.rds.id],
    [aws_route_table.custom.id]
  )

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-vpce-s3"
    Type = "VPCEndpoint"
    Service = "S3"
  })
}

#==============================================================================
# NETWORK ACLS (Optional - Default NACL allows all traffic)
#==============================================================================

# Custom Network ACL for additional security (optional)
resource "aws_network_acl" "main" {
  vpc_id = aws_vpc.main.id

  # Allow all inbound traffic (can be customized)
  ingress {
    protocol   = "-1"
    rule_no    = 100
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = 0
    to_port    = 0
  }

  # Allow all outbound traffic (can be customized)
  egress {
    protocol   = "-1"
    rule_no    = 100
    action     = "allow"
    cidr_block = "0.0.0.0/0"
    from_port  = 0
    to_port    = 0
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-nacl"
    Type = "NetworkACL"
  })
}
