{"version": 4, "terraform_version": "1.12.1", "serial": 28, "lineage": "68f2ce55-96b5-a670-b8f2-3610c2a1d168", "outputs": {"availability_zones": {"value": ["ap-northeast-1a", "ap-northeast-1c"], "type": ["list", "string"]}, "custom_route_table_id": {"value": "rtb-093fc4a9b93575fd8", "type": "string"}, "db_subnet_group_id": {"value": "dev-entanglement-vpc-db-subnet-group", "type": "string"}, "db_subnet_group_name": {"value": "dev-entanglement-vpc-db-subnet-group", "type": "string"}, "elastic_ip_id": {"value": "eipalloc-0216358dfb651da30", "type": "string"}, "elastic_ip_public_ip": {"value": "**************", "type": "string"}, "internet_gateway_arn": {"value": "arn:aws:ec2:ap-northeast-1:************:internet-gateway/igw-0a3b7adb756b99f66", "type": "string"}, "internet_gateway_id": {"value": "igw-0a3b7adb756b99f66", "type": "string"}, "nat_gateway_id": {"value": "nat-06d09ae7c16c72f27", "type": "string"}, "nat_gateway_private_ip": {"value": "**********", "type": "string"}, "nat_gateway_public_ip": {"value": "**************", "type": "string"}, "network_acl_id": {"value": "acl-003d8473edc04a0af", "type": "string"}, "private_route_table_ids": {"value": ["rtb-0494a2e42d9c29959", "rtb-0e0973bbea45af00c"], "type": ["tuple", ["string", "string"]]}, "private_subnet_arns": {"value": ["arn:aws:ec2:ap-northeast-1:************:subnet/subnet-07e1b835f87a93fc6", "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-02a7355219bcb1c05"], "type": ["tuple", ["string", "string"]]}, "private_subnet_cidr_blocks": {"value": ["*********/24", "*********/24"], "type": ["tuple", ["string", "string"]]}, "private_subnet_ids": {"value": ["subnet-07e1b835f87a93fc6", "subnet-02a7355219bcb1c05"], "type": ["tuple", ["string", "string"]]}, "public_route_table_id": {"value": "rtb-05724a29b84db4ddf", "type": "string"}, "public_subnet_arns": {"value": ["arn:aws:ec2:ap-northeast-1:************:subnet/subnet-068954d578fbdb2b0", "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-0a19e6b5e3722aa2d"], "type": ["tuple", ["string", "string"]]}, "public_subnet_cidr_blocks": {"value": ["********/24", "********/24"], "type": ["tuple", ["string", "string"]]}, "public_subnet_ids": {"value": ["subnet-068954d578fbdb2b0", "subnet-0a19e6b5e3722aa2d"], "type": ["tuple", ["string", "string"]]}, "rds_route_table_id": {"value": "rtb-0271447c36e7a33e3", "type": "string"}, "rds_subnet_arns": {"value": ["arn:aws:ec2:ap-northeast-1:************:subnet/subnet-0bab9e26d27e8734a", "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-0e2dd579fe1fcfb1e"], "type": ["tuple", ["string", "string"]]}, "rds_subnet_cidr_blocks": {"value": ["**********/24", "**********/24"], "type": ["tuple", ["string", "string"]]}, "rds_subnet_ids": {"value": ["subnet-0bab9e26d27e8734a", "subnet-0e2dd579fe1fcfb1e"], "type": ["tuple", ["string", "string"]]}, "s3_vpc_endpoint_id": {"value": "vpce-058f34413ed43046e", "type": "string"}, "s3_vpc_endpoint_prefix_list_id": {"value": "pl-61a54008", "type": "string"}, "vpc_arn": {"value": "arn:aws:ec2:ap-northeast-1:************:vpc/vpc-004f8c1647903ccca", "type": "string"}, "vpc_cidr_block": {"value": "10.0.0.0/16", "type": "string"}, "vpc_default_network_acl_id": {"value": "acl-0ca858bc55e1b359f", "type": "string"}, "vpc_default_route_table_id": {"value": "rtb-0edf3052a6c9eca87", "type": "string"}, "vpc_default_security_group_id": {"value": "sg-03955da75ef5f8999", "type": "string"}, "vpc_id": {"value": "vpc-004f8c1647903ccca", "type": "string"}, "vpc_summary": {"value": {"nat_gateway_enabled": true, "private_subnets_count": 2, "public_subnets_count": 2, "rds_subnets_count": 2, "route_tables_count": 5, "s3_endpoint_enabled": true, "vpc_cidr": "10.0.0.0/16", "vpc_id": "vpc-004f8c1647903ccca"}, "type": ["object", {"nat_gateway_enabled": "bool", "private_subnets_count": "number", "public_subnets_count": "number", "rds_subnets_count": "number", "route_tables_count": "number", "s3_endpoint_enabled": "bool", "vpc_cidr": "string", "vpc_id": "string"}]}}, "resources": [{"mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/<EMAIL>", "id": "************", "user_id": "AIDAUSJEUSJY4RSKL7JDP"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "Asia Pacific (Tokyo)", "endpoint": "ec2.ap-northeast-1.amazonaws.com", "id": "ap-northeast-1", "name": "ap-northeast-1", "region": "ap-northeast-1"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "aws_db_subnet_group", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:rds:ap-northeast-1:************:subgrp:dev-entanglement-vpc-db-subnet-group", "description": "Managed by Terraform", "id": "dev-entanglement-vpc-db-subnet-group", "name": "dev-entanglement-vpc-db-subnet-group", "name_prefix": "", "region": "ap-northeast-1", "subnet_ids": ["subnet-0bab9e26d27e8734a", "subnet-0e2dd579fe1fcfb1e"], "supported_network_types": ["IPV4"], "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-db-subnet-group", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "DBSubnetGroup"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-db-subnet-group", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "DBSubnetGroup"}, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_subnet.rds", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-0216358dfb651da30", "arn": "arn:aws:ec2:ap-northeast-1:************:elastic-ip/eipalloc-0216358dfb651da30", "associate_with_private_ip": null, "association_id": "", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-0216358dfb651da30", "instance": "", "ipam_pool_id": null, "network_border_group": "ap-northeast-1", "network_interface": "", "private_dns": null, "private_ip": "", "ptr_record": "", "public_dns": "ec2-52-197-187-253.ap-northeast-1.compute.amazonaws.com", "public_ip": "**************", "public_ipv4_pool": "amazon", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-nat-eip", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "ElasticIP"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-nat-eip", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "ElasticIP"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_internet_gateway.main", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_internet_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:internet-gateway/igw-0a3b7adb756b99f66", "id": "igw-0a3b7adb756b99f66", "owner_id": "************", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-igw", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "InternetGateway"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-igw", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "InternetGateway"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_nat_gateway", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-0216358dfb651da30", "association_id": "eipassoc-063e8b348d738dcbf", "connectivity_type": "public", "id": "nat-06d09ae7c16c72f27", "network_interface_id": "eni-085ffa6bd49015915", "private_ip": "**********", "public_ip": "**************", "region": "ap-northeast-1", "secondary_allocation_ids": null, "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-068954d578fbdb2b0", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-nat-gateway", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "NATGateway"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-nat-gateway", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "NATGateway"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_subnet.public", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_network_acl", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:network-acl/acl-003d8473edc04a0af", "egress": [{"action": "allow", "cidr_block": "0.0.0.0/0", "from_port": 0, "icmp_code": null, "icmp_type": null, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "id": "acl-003d8473edc04a0af", "ingress": [{"action": "allow", "cidr_block": "0.0.0.0/0", "from_port": 0, "icmp_code": null, "icmp_type": null, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "owner_id": "************", "region": "ap-northeast-1", "subnet_ids": [], "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-nacl", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "NetworkACL"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-nacl", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "NetworkACL"}, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route", "name": "private_nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "0.0.0.0/0", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "id": "r-rtb-0494a2e42d9c299591080289494", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "nat-06d09ae7c16c72f27", "network_interface_id": "", "origin": "CreateRoute", "region": "ap-northeast-1", "route_table_id": "rtb-0494a2e42d9c29959", "state": "active", "timeouts": null, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.public", "aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "0.0.0.0/0", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "id": "r-rtb-0e0973bbea45af00c1080289494", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "nat-06d09ae7c16c72f27", "network_interface_id": "", "origin": "CreateRoute", "region": "ap-northeast-1", "route_table_id": "rtb-0e0973bbea45af00c", "state": "active", "timeouts": null, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_eip.nat", "aws_internet_gateway.main", "aws_nat_gateway.main", "aws_route_table.private", "aws_subnet.public", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route", "name": "public_internet", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "0.0.0.0/0", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-0a3b7adb756b99f66", "id": "r-rtb-05724a29b84db4ddf1080289494", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "origin": "CreateRoute", "region": "ap-northeast-1", "route_table_id": "rtb-05724a29b84db4ddf", "state": "active", "timeouts": null, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_internet_gateway.main", "aws_route_table.public", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "custom", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:route-table/rtb-093fc4a9b93575fd8", "id": "rtb-093fc4a9b93575fd8", "owner_id": "************", "propagating_vgws": [], "region": "ap-northeast-1", "route": [], "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-rtb-custom", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "CustomRouteTable"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-rtb-custom", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "CustomRouteTable"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:route-table/rtb-0494a2e42d9c29959", "id": "rtb-0494a2e42d9c29959", "owner_id": "************", "propagating_vgws": [], "region": "ap-northeast-1", "route": [], "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-rtb-private1-ap-northeast-1a", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "PrivateRouteTable"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-rtb-private1-ap-northeast-1a", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "PrivateRouteTable"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:route-table/rtb-0e0973bbea45af00c", "id": "rtb-0e0973bbea45af00c", "owner_id": "************", "propagating_vgws": [], "region": "ap-northeast-1", "route": [], "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-rtb-private2-ap-northeast-1c", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "PrivateRouteTable"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-rtb-private2-ap-northeast-1c", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "PrivateRouteTable"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:route-table/rtb-05724a29b84db4ddf", "id": "rtb-05724a29b84db4ddf", "owner_id": "************", "propagating_vgws": [], "region": "ap-northeast-1", "route": [], "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-rtb-public", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "PublicRouteTable"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-rtb-public", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "PublicRouteTable"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table", "name": "rds", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:route-table/rtb-0271447c36e7a33e3", "id": "rtb-0271447c36e7a33e3", "owner_id": "************", "propagating_vgws": [], "region": "ap-northeast-1", "route": [], "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "RDS-Pvt-rt", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "DatabaseRouteTable"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "RDS-Pvt-rt", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "DatabaseRouteTable"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-00210b476ac09d2cb", "region": "ap-northeast-1", "route_table_id": "rtb-0494a2e42d9c29959", "subnet_id": "subnet-07e1b835f87a93fc6", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.private", "aws_subnet.private", "aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-006dde5ddf1426390", "region": "ap-northeast-1", "route_table_id": "rtb-0e0973bbea45af00c", "subnet_id": "subnet-02a7355219bcb1c05", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.private", "aws_subnet.private", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-002409c32a9fffaed", "region": "ap-northeast-1", "route_table_id": "rtb-05724a29b84db4ddf", "subnet_id": "subnet-068954d578fbdb2b0", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.public", "aws_subnet.public", "aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0ee32a0da9ba5364d", "region": "ap-northeast-1", "route_table_id": "rtb-05724a29b84db4ddf", "subnet_id": "subnet-0a19e6b5e3722aa2d", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.public", "aws_subnet.public", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_route_table_association", "name": "rds", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0036dc037e3a09682", "region": "ap-northeast-1", "route_table_id": "rtb-0271447c36e7a33e3", "subnet_id": "subnet-0bab9e26d27e8734a", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.rds", "aws_subnet.rds", "aws_vpc.main"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-0bb570ec3bea80e48", "region": "ap-northeast-1", "route_table_id": "rtb-0271447c36e7a33e3", "subnet_id": "subnet-0e2dd579fe1fcfb1e", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.rds", "aws_subnet.rds", "aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-07e1b835f87a93fc6", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1a", "availability_zone_id": "apne1-az4", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-07e1b835f87a93fc6", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-subnet-private1-ap-northeast-1a", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Private", "Type": "PrivateSubnet"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-subnet-private1-ap-northeast-1a", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Private", "Type": "PrivateSubnet"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-02a7355219bcb1c05", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1c", "availability_zone_id": "apne1-az1", "cidr_block": "*********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-02a7355219bcb1c05", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-subnet-private2-ap-northeast-1c", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Private", "Type": "PrivateSubnet"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-subnet-private2-ap-northeast-1c", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Private", "Type": "PrivateSubnet"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-068954d578fbdb2b0", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1a", "availability_zone_id": "apne1-az4", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-068954d578fbdb2b0", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-subnet-public1-ap-northeast-1a", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Public", "Type": "PublicSubnet"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-subnet-public1-ap-northeast-1a", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Public", "Type": "PublicSubnet"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-0a19e6b5e3722aa2d", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1c", "availability_zone_id": "apne1-az1", "cidr_block": "********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0a19e6b5e3722aa2d", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": true, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-subnet-public2-ap-northeast-1c", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Public", "Type": "PublicSubnet"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-subnet-public2-ap-northeast-1c", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Public", "Type": "PublicSubnet"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_subnet", "name": "rds", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-0bab9e26d27e8734a", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1a", "availability_zone_id": "apne1-az4", "cidr_block": "**********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0bab9e26d27e8734a", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "RDS-Pvt-subnet-1", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Database", "Type": "DatabaseSubnet"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "RDS-Pvt-subnet-1", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Database", "Type": "DatabaseSubnet"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:subnet/subnet-0e2dd579fe1fcfb1e", "assign_ipv6_address_on_creation": false, "availability_zone": "ap-northeast-1c", "availability_zone_id": "apne1-az1", "cidr_block": "**********/24", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0e2dd579fe1fcfb1e", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "RDS-Pvt-subnet-2", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Database", "Type": "DatabaseSubnet"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "RDS-Pvt-subnet-2", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Tier": "Database", "Type": "DatabaseSubnet"}, "timeouts": null, "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["aws_vpc.main"]}]}, {"mode": "managed", "type": "aws_vpc", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:vpc/vpc-004f8c1647903ccca", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-0ca858bc55e1b359f", "default_route_table_id": "rtb-0edf3052a6c9eca87", "default_security_group_id": "sg-03955da75ef5f8999", "dhcp_options_id": "dopt-0ced1e466dc80e32d", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-004f8c1647903ccca", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0edf3052a6c9eca87", "owner_id": "************", "region": "ap-northeast-1", "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "VPC"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc", "Owner": "DevOps Team", "Project": "entanglement", "Terraform": "true", "Type": "VPC"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ=="}]}, {"mode": "managed", "type": "aws_vpc_endpoint", "name": "s3", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:ap-northeast-1:************:vpc-endpoint/vpce-058f34413ed43046e", "auto_accept": null, "cidr_blocks": ["*********/21", "************/23", "**********/20", "***********/22", "************/22", "************/22", "************/24", "************/23", "************/23", "***********/24", "************/22", "************/22", "***********/22"], "dns_entry": [], "dns_options": [], "id": "vpce-058f34413ed43046e", "ip_address_type": "", "network_interface_ids": [], "owner_id": "************", "policy": "{\"Statement\":[{\"Action\":\"*\",\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"*\"}],\"Version\":\"2008-10-17\"}", "prefix_list_id": "pl-61a54008", "private_dns_enabled": false, "region": "ap-northeast-1", "requester_managed": false, "resource_configuration_arn": "", "route_table_ids": ["rtb-0271447c36e7a33e3", "rtb-0494a2e42d9c29959", "rtb-05724a29b84db4ddf", "rtb-093fc4a9b93575fd8", "rtb-0e0973bbea45af00c"], "security_group_ids": [], "service_name": "com.amazonaws.ap-northeast-1.s3", "service_network_arn": "", "service_region": "ap-northeast-1", "state": "available", "subnet_configuration": [], "subnet_ids": [], "tags": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-vpce-s3", "Owner": "DevOps Team", "Project": "entanglement", "Service": "S3", "Terraform": "true", "Type": "VPCEndpoint"}, "tags_all": {"CostCenter": "Engineering", "Description": "Development VPC for entanglement project", "Environment": "dev", "ManagedBy": "Terraform", "Name": "dev-entanglement-vpc-vpce-s3", "Owner": "DevOps Team", "Project": "entanglement", "Service": "S3", "Terraform": "true", "Type": "VPCEndpoint"}, "timeouts": null, "vpc_endpoint_type": "Gateway", "vpc_id": "vpc-004f8c1647903ccca"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_route_table.custom", "aws_route_table.private", "aws_route_table.public", "aws_route_table.rds", "aws_vpc.main", "data.aws_region.current"]}]}], "check_results": null}