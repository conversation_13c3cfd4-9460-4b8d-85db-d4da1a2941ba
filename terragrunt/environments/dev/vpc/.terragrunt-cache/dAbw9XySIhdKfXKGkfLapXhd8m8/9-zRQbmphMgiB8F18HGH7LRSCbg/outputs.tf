# VPC module outputs

#==============================================================================
# VPC OUTPUTS
#==============================================================================

output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.main.arn
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "vpc_default_security_group_id" {
  description = "ID of the default security group"
  value       = aws_vpc.main.default_security_group_id
}

output "vpc_default_network_acl_id" {
  description = "ID of the default network ACL"
  value       = aws_vpc.main.default_network_acl_id
}

output "vpc_default_route_table_id" {
  description = "ID of the default route table"
  value       = aws_vpc.main.default_route_table_id
}

#==============================================================================
# INTERNET GATEWAY OUTPUTS
#==============================================================================

output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = aws_internet_gateway.main.id
}

output "internet_gateway_arn" {
  description = "ARN of the Internet Gateway"
  value       = aws_internet_gateway.main.arn
}

#==============================================================================
# SUBNET OUTPUTS
#==============================================================================

output "public_subnet_ids" {
  description = "List of IDs of the public subnets"
  value       = aws_subnet.public[*].id
}

output "public_subnet_arns" {
  description = "List of ARNs of the public subnets"
  value       = aws_subnet.public[*].arn
}

output "public_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the public subnets"
  value       = aws_subnet.public[*].cidr_block
}

output "private_subnet_ids" {
  description = "List of IDs of the private subnets"
  value       = aws_subnet.private[*].id
}

output "private_subnet_arns" {
  description = "List of ARNs of the private subnets"
  value       = aws_subnet.private[*].arn
}

output "private_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the private subnets"
  value       = aws_subnet.private[*].cidr_block
}

output "rds_subnet_ids" {
  description = "List of IDs of the RDS subnets"
  value       = aws_subnet.rds[*].id
}

output "rds_subnet_arns" {
  description = "List of ARNs of the RDS subnets"
  value       = aws_subnet.rds[*].arn
}

output "rds_subnet_cidr_blocks" {
  description = "List of CIDR blocks of the RDS subnets"
  value       = aws_subnet.rds[*].cidr_block
}

output "db_subnet_group_id" {
  description = "ID of the database subnet group"
  value       = aws_db_subnet_group.main.id
}

output "db_subnet_group_name" {
  description = "Name of the database subnet group"
  value       = aws_db_subnet_group.main.name
}

#==============================================================================
# NAT GATEWAY OUTPUTS
#==============================================================================

output "nat_gateway_id" {
  description = "ID of the NAT Gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].id : null
}

output "nat_gateway_public_ip" {
  description = "Public IP address of the NAT Gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].public_ip : null
}

output "nat_gateway_private_ip" {
  description = "Private IP address of the NAT Gateway"
  value       = var.enable_nat_gateway ? aws_nat_gateway.main[0].private_ip : null
}

output "elastic_ip_id" {
  description = "ID of the Elastic IP for NAT Gateway"
  value       = var.enable_nat_gateway ? aws_eip.nat[0].id : null
}

output "elastic_ip_public_ip" {
  description = "Public IP address of the Elastic IP"
  value       = var.enable_nat_gateway ? aws_eip.nat[0].public_ip : null
}

#==============================================================================
# ROUTE TABLE OUTPUTS
#==============================================================================

output "public_route_table_id" {
  description = "ID of the public route table"
  value       = aws_route_table.public.id
}

output "private_route_table_ids" {
  description = "List of IDs of the private route tables"
  value       = aws_route_table.private[*].id
}

output "rds_route_table_id" {
  description = "ID of the RDS route table"
  value       = aws_route_table.rds.id
}

output "custom_route_table_id" {
  description = "ID of the custom route table"
  value       = aws_route_table.custom.id
}

#==============================================================================
# VPC ENDPOINT OUTPUTS
#==============================================================================

output "s3_vpc_endpoint_id" {
  description = "ID of the S3 VPC Endpoint"
  value       = var.enable_s3_endpoint ? aws_vpc_endpoint.s3[0].id : null
}

output "s3_vpc_endpoint_prefix_list_id" {
  description = "Prefix list ID of the S3 VPC Endpoint"
  value       = var.enable_s3_endpoint ? aws_vpc_endpoint.s3[0].prefix_list_id : null
}

#==============================================================================
# NETWORK ACL OUTPUTS
#==============================================================================

output "network_acl_id" {
  description = "ID of the custom Network ACL"
  value       = aws_network_acl.main.id
}

#==============================================================================
# AVAILABILITY ZONE OUTPUTS
#==============================================================================

output "availability_zones" {
  description = "List of availability zones used"
  value       = var.availability_zones
}

#==============================================================================
# SUMMARY OUTPUTS
#==============================================================================

output "vpc_summary" {
  description = "Summary of VPC resources created"
  value = {
    vpc_id                = aws_vpc.main.id
    vpc_cidr              = aws_vpc.main.cidr_block
    public_subnets_count  = length(aws_subnet.public)
    private_subnets_count = length(aws_subnet.private)
    rds_subnets_count     = length(aws_subnet.rds)
    route_tables_count    = 1 + length(aws_route_table.private) + 2  # public + private + rds + custom
    nat_gateway_enabled   = var.enable_nat_gateway
    s3_endpoint_enabled   = var.enable_s3_endpoint
  }
}
