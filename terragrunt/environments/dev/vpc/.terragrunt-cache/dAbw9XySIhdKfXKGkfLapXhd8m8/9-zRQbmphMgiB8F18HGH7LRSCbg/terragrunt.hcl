# VPC configuration for dev environment

include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../terraform/vpc"
}

# Local values
locals {
  region = "ap-northeast-1"
}

inputs = {
  environment    = "dev"
  project_name   = "entanglement"
  aws_region     = local.region

  # VPC Configuration
  vpc_cidr = "10.0.0.0/16"

  # Availability Zones (ap-northeast-1a and ap-northeast-1c)
  availability_zones = [
    "ap-northeast-1a",
    "ap-northeast-1c"
  ]

  # Public Subnets (2 subnets)
  public_subnet_cidrs = [
    "10.0.1.0/24",   # dev-entanglement-vpc-subnet-public1-ap-northeast-1a
    "10.0.2.0/24"    # dev-entanglement-vpc-subnet-public2-ap-northeast-1c
  ]

  # Private Application Subnets (2 subnets)
  private_subnet_cidrs = [
    "*********/24",  # dev-entanglement-vpc-subnet-private1-ap-northeast-1a
    "*********/24"   # dev-entanglement-vpc-subnet-private2-ap-northeast-1c
  ]

  # RDS Private Subnets (2 subnets across ap-northeast-1a and ap-northeast-1c)
  rds_subnet_cidrs = [
    "**********/24", # RDS-Pvt-subnet-1 (ap-northeast-1a)
    "**********/24"  # RDS-Pvt-subnet-2 (ap-northeast-1c)
  ]

  # DNS Configuration
  enable_dns_hostnames = true
  enable_dns_support   = true

  # Network Components
  enable_nat_gateway  = true  # Creates dev-entanglement-vpc-nat-gateway
  enable_s3_endpoint  = true  # Creates dev-entanglement-vpc-vpce-s3

  # Common tags
  tags = {
    Environment = "dev"
    Project     = "entanglement"
    Owner       = "DevOps Team"
    CostCenter  = "Engineering"
    Terraform   = "true"
    Description = "Development VPC for entanglement project"
  }
}
