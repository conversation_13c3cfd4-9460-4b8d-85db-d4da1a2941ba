# VPC Module

This module creates a comprehensive AWS VPC infrastructure with public subnets, private application subnets, RDS database subnets, NAT Gateway, Internet Gateway, and S3 VPC Endpoint.

## Architecture Overview

```
Internet Gateway
    ↓
Public Subnets (2) - us-east-1a, us-east-1b
    ↓
NAT Gateway
    ↓
Private Application Subnets (2) - us-east-1a, us-east-1b
    ↓
RDS Private Subnets (5) - us-east-1a to us-east-1e
```

## Resources Created

### Network Infrastructure (9 Subnets Total)

#### Public Subnets (2)
- `dev-entanglement-vpc-subnet-public1-us-east-1a`
- `dev-entanglement-vpc-subnet-public2-us-east-1b`

#### Private Application Subnets (2)
- `dev-entanglement-vpc-subnet-private1-us-east-1a`
- `dev-entanglement-vpc-subnet-private2-us-east-1b`

#### RDS Private Subnets (5)
- `RDS-Pvt-subnet-1` (us-east-1a)
- `RDS-Pvt-subnet-2` (us-east-1b)
- `RDS-Pvt-subnet-3` (us-east-1c)
- `RDS-Pvt-subnet-4` (us-east-1d)
- `RDS-Pvt-subnet-5` (us-east-1e)

### Route Tables (5 Total)
- `dev-entanglement-vpc-rtb-public` - For public subnets
- `dev-entanglement-vpc-rtb-private1-us-east-1a` - For private subnet 1
- `dev-entanglement-vpc-rtb-private2-us-east-1b` - For private subnet 2
- `RDS-Pvt-rt` - For RDS database subnets
- `dev-entanglement-vpc-rtb-custom` - Additional custom route table

### Network Connections (3)
- `dev-entanglement-vpc-igw` - Internet Gateway for public access
- `dev-entanglement-vpc-nat-gateway` - NAT Gateway for private subnet internet access
- `dev-entanglement-vpc-vpce-s3` - VPC Endpoint to Amazon S3

### Additional Resources
- DB Subnet Group for RDS
- Elastic IP for NAT Gateway
- Network ACL for additional security
- Route table associations

## Usage

### Basic Usage
```hcl
module "vpc" {
  source = "./terraform/vpc"
  
  environment    = "dev"
  project_name   = "entanglement"
  aws_region     = "us-east-1"
  
  vpc_cidr = "10.0.0.0/16"
  
  public_subnet_cidrs = [
    "********/24",
    "********/24"
  ]
  
  private_subnet_cidrs = [
    "*********/24",
    "*********/24"
  ]
  
  rds_subnet_cidrs = [
    "**********/24",
    "**********/24",
    "**********/24",
    "**********/24",
    "**********/24"
  ]
  
  enable_nat_gateway = true
  enable_s3_endpoint = true
  
  tags = {
    Environment = "dev"
    Project     = "entanglement"
  }
}
```

### Terragrunt Usage
```hcl
include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../terraform/vpc"
}

inputs = {
  environment    = "dev"
  project_name   = "entanglement"
  vpc_cidr       = "10.0.0.0/16"
  
  availability_zones = [
    "us-east-1a", "us-east-1b", "us-east-1c",
    "us-east-1d", "us-east-1e", "us-east-1f"
  ]
  
  enable_nat_gateway = true
  enable_s3_endpoint = true
}
```

## Variables

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| environment | Environment name | `string` | `"dev"` | no |
| project_name | Name of the project | `string` | `"entanglement"` | no |
| aws_region | AWS region | `string` | `"us-east-1"` | no |
| vpc_cidr | CIDR block for VPC | `string` | `"10.0.0.0/16"` | no |
| availability_zones | List of availability zones | `list(string)` | `[...]` | no |
| public_subnet_cidrs | CIDR blocks for public subnets | `list(string)` | `[...]` | no |
| private_subnet_cidrs | CIDR blocks for private subnets | `list(string)` | `[...]` | no |
| rds_subnet_cidrs | CIDR blocks for RDS subnets | `list(string)` | `[...]` | no |
| enable_dns_hostnames | Enable DNS hostnames | `bool` | `true` | no |
| enable_dns_support | Enable DNS support | `bool` | `true` | no |
| enable_nat_gateway | Enable NAT Gateway | `bool` | `true` | no |
| enable_s3_endpoint | Enable S3 VPC Endpoint | `bool` | `true` | no |
| tags | Common tags | `map(string)` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| vpc_id | ID of the VPC |
| vpc_cidr_block | CIDR block of the VPC |
| public_subnet_ids | List of public subnet IDs |
| private_subnet_ids | List of private subnet IDs |
| rds_subnet_ids | List of RDS subnet IDs |
| db_subnet_group_name | Name of the DB subnet group |
| internet_gateway_id | ID of the Internet Gateway |
| nat_gateway_id | ID of the NAT Gateway |
| nat_gateway_public_ip | Public IP of the NAT Gateway |
| public_route_table_id | ID of the public route table |
| private_route_table_ids | List of private route table IDs |
| rds_route_table_id | ID of the RDS route table |
| s3_vpc_endpoint_id | ID of the S3 VPC Endpoint |
| vpc_summary | Summary of all VPC resources |

## Network Design

### CIDR Allocation
- **VPC**: `10.0.0.0/16` (65,536 IPs)
- **Public Subnets**: `********/24`, `********/24` (256 IPs each)
- **Private Subnets**: `*********/24`, `*********/24` (256 IPs each)
- **RDS Subnets**: `**********/24` to `**********/24` (256 IPs each)

### Routing
- **Public Subnets**: Route to Internet Gateway for internet access
- **Private Subnets**: Route to NAT Gateway for outbound internet access
- **RDS Subnets**: Isolated, no internet access
- **S3 Endpoint**: Gateway endpoint for efficient S3 access

### Security
- Network ACLs for additional layer of security
- Separate route tables for different subnet tiers
- VPC Endpoints to reduce internet traffic

## Examples

### Using VPC Outputs in Other Modules
```hcl
# In another module
resource "aws_instance" "web" {
  subnet_id = module.vpc.public_subnet_ids[0]
  # ... other configuration
}

resource "aws_db_instance" "main" {
  db_subnet_group_name = module.vpc.db_subnet_group_name
  # ... other configuration
}
```

## Requirements

| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | >= 5.0 |

## Authors

Module managed by DevOps Team.

## License

Apache 2 Licensed. See LICENSE for full details.
