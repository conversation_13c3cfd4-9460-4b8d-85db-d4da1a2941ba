# VPC configuration for dev environment

include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../../terraform/vpc"
}

# Local values
locals {
  region = "us-east-1"
}

inputs = {
  environment    = "dev"
  project_name   = "entanglement"
  aws_region     = local.region

  # VPC Configuration
  vpc_cidr = "10.0.0.0/16"

  # Availability Zones (us-east-1a to us-east-1f)
  availability_zones = [
    "us-east-1a",
    "us-east-1b",
    "us-east-1c",
    "us-east-1d",
    "us-east-1e",
    "us-east-1f"
  ]

  # Public Subnets (2 subnets)
  public_subnet_cidrs = [
    "10.0.1.0/24",   # dev-entanglement-vpc-subnet-public1-us-east-1a
    "10.0.2.0/24"    # dev-entanglement-vpc-subnet-public2-us-east-1b
  ]

  # Private Application Subnets (2 subnets)
  private_subnet_cidrs = [
    "10.0.10.0/24",  # dev-entanglement-vpc-subnet-private1-us-east-1a
    "*********/24"   # dev-entanglement-vpc-subnet-private2-us-east-1b
  ]

  # RDS Private Subnets (5 subnets across us-east-1a to us-east-1e)
  rds_subnet_cidrs = [
    "**********/24", # RDS-Pvt-subnet-1 (us-east-1a)
    "**********/24", # RDS-Pvt-subnet-2 (us-east-1b)
    "**********/24", # RDS-Pvt-subnet-3 (us-east-1c)
    "**********/24", # RDS-Pvt-subnet-4 (us-east-1d)
    "**********/24"  # RDS-Pvt-subnet-5 (us-east-1e)
  ]

  # DNS Configuration
  enable_dns_hostnames = true
  enable_dns_support   = true

  # Network Components
  enable_nat_gateway  = true  # Creates dev-entanglement-vpc-nat-gateway
  enable_s3_endpoint  = true  # Creates dev-entanglement-vpc-vpce-s3

  # Common tags
  tags = {
    Environment = "dev"
    Project     = "entanglement"
    Owner       = "DevOps Team"
    CostCenter  = "Engineering"
    Terraform   = "true"
    Description = "Development VPC for entanglement project"
  }
}
