# 🔧 Terraform & Terragrunt Troubleshooting Guide

## 🎯 Common Issues & Solutions

### 1. **State-Related Issues**

#### State Lock Issues
```bash
# Problem: State is locked
Error: Error acquiring the state lock

# Solution 1: Wait for lock to release naturally
# Solution 2: Force unlock (use with caution)
terraform force-unlock LOCK_ID

# Solution 3: Check DynamoDB for stuck locks
aws dynamodb scan --table-name terraform-locks
```

#### State Drift
```bash
# Problem: Infrastructure doesn't match state
# Solution: Refresh state and plan
terraform refresh
terraform plan

# Import existing resources
terraform import aws_instance.example i-1234567890abcdef0

# Remove resources from state (without destroying)
terraform state rm aws_instance.example
```

#### Corrupted State
```bash
# Problem: State file is corrupted
# Solution: Restore from backup
aws s3 cp s3://terraform-state-bucket/path/terraform.tfstate.backup terraform.tfstate

# Or use state versioning
aws s3api list-object-versions --bucket terraform-state-bucket --prefix path/terraform.tfstate
```

### 2. **Terragrunt-Specific Issues**

#### Dependency Cycles
```bash
# Problem: Circular dependency detected
# Solution: Review and fix dependency graph
terragrunt dag graph | dot -Tpng > dependencies.png

# Check for cycles
terragrunt dag validate
```

#### Mock Outputs Not Working
```hcl
# Problem: Mock outputs not being used
# Solution: Ensure proper configuration
dependency "vpc" {
  config_path = "../vpc"
  
  mock_outputs = {
    vpc_id = "vpc-fake-id"
  }
  # Add this line for plan/validate commands
  mock_outputs_allowed_terraform_commands = ["validate", "plan"]
}
```

#### Include Path Issues
```bash
# Problem: Cannot find parent terragrunt.hcl
# Solution: Check file structure and paths
find . -name "terragrunt.hcl" -type f

# Debug include resolution
terragrunt run-all plan --terragrunt-log-level debug
```

### 3. **AWS Provider Issues**

#### Authentication Problems
```bash
# Problem: Unable to authenticate with AWS
# Solution: Check credentials
aws sts get-caller-identity

# Check environment variables
echo $AWS_ACCESS_KEY_ID
echo $AWS_SECRET_ACCESS_KEY
echo $AWS_REGION

# Use AWS CLI profile
export AWS_PROFILE=your-profile
```

#### Rate Limiting
```bash
# Problem: AWS API rate limiting
Error: RequestLimitExceeded

# Solution: Add retry configuration
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  retry_mode = "adaptive"
  max_retries = 10
}
```

#### Resource Conflicts
```bash
# Problem: Resource already exists
Error: InvalidGroup.Duplicate

# Solution: Import existing resource
terraform import aws_security_group.example sg-1234567890abcdef0

# Or use data source instead
data "aws_security_group" "existing" {
  name = "existing-security-group"
}
```

### 4. **Module Issues**

#### Module Source Problems
```bash
# Problem: Cannot download module
# Solution: Check source URL and authentication
terraform get -update

# For private repositories
git config --global url."https://oauth2:${GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/"
```

#### Version Conflicts
```hcl
# Problem: Module version conflicts
# Solution: Pin specific versions
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "= 5.0.0"  # Exact version
    }
  }
}

module "vpc" {
  source  = "terraform-aws-modules/vpc/aws"
  version = "= 3.14.0"  # Pin module version
}
```

### 5. **Performance Issues**

#### Slow Terraform Operations
```bash
# Problem: Terraform runs are slow
# Solution: Enable provider caching
export TF_PLUGIN_CACHE_DIR="$HOME/.terraform.d/plugin-cache"
mkdir -p $TF_PLUGIN_CACHE_DIR

# Use parallelism
terraform apply -parallelism=10

# For Terragrunt
terragrunt run-all apply --terragrunt-parallelism 5
```

#### Large State Files
```bash
# Problem: State file is too large
# Solution: Split into smaller states
# Use separate state files per service/environment

# Remove unused resources from state
terraform state list | grep "unused_resource"
terraform state rm aws_instance.unused
```

### 6. **Debugging Techniques**

#### Enable Debug Logging
```bash
# Terraform debug logging
export TF_LOG=DEBUG
export TF_LOG_PATH=terraform.log

# Terragrunt debug logging
terragrunt plan --terragrunt-log-level debug

# AWS provider debug logging
export TF_LOG_PROVIDER=DEBUG
```

#### Validate Configurations
```bash
# Validate Terraform syntax
terraform validate

# Validate Terragrunt configuration
terragrunt validate

# Check formatting
terraform fmt -check -recursive
terragrunt hcl format --check
```

#### Plan Analysis
```bash
# Generate detailed plan
terraform plan -out=tfplan
terraform show -json tfplan | jq '.'

# Show current state
terraform show
terraform state list
terraform state show aws_instance.example
```

### 7. **Common Error Messages & Solutions**

#### "Resource Not Found"
```bash
# Error: Resource was deleted outside Terraform
# Solution: Remove from state or recreate
terraform state rm aws_instance.deleted
# OR
terraform apply  # Will recreate the resource
```

#### "Insufficient Permissions"
```bash
# Error: User/Role doesn't have required permissions
# Solution: Check and update IAM policies
aws iam simulate-principal-policy \
  --policy-source-arn arn:aws:iam::123456789012:user/terraform \
  --action-names ec2:RunInstances \
  --resource-arns "*"
```

#### "Invalid CIDR Block"
```bash
# Error: CIDR block conflicts or invalid
# Solution: Check CIDR ranges
aws ec2 describe-vpcs --query 'Vpcs[*].CidrBlock'

# Use terraform console to test
terraform console
> cidrsubnet("10.0.0.0/16", 8, 1)
```

### 8. **Recovery Procedures**

#### Disaster Recovery
```bash
# 1. Backup current state
aws s3 cp s3://terraform-state-bucket/terraform.tfstate ./backup/

# 2. Restore from backup if needed
aws s3 cp ./backup/terraform.tfstate s3://terraform-state-bucket/

# 3. Verify state integrity
terraform plan
```

#### State Surgery (Advanced)
```bash
# Move resources between states
terraform state mv aws_instance.old aws_instance.new

# Replace provider in state
terraform state replace-provider registry.terraform.io/-/aws hashicorp/aws

# Pull remote state to local
terraform state pull > terraform.tfstate.backup
```

### 9. **Monitoring & Alerting**

#### CloudWatch Monitoring
```hcl
# Monitor Terraform operations
resource "aws_cloudwatch_log_group" "terraform" {
  name              = "/aws/terraform/operations"
  retention_in_days = 30
}

# CloudTrail for API monitoring
resource "aws_cloudtrail" "terraform" {
  name           = "terraform-operations"
  s3_bucket_name = aws_s3_bucket.cloudtrail.bucket
  
  event_selector {
    read_write_type           = "All"
    include_management_events = true
  }
}
```

#### Cost Monitoring
```bash
# Monitor unexpected costs
aws ce get-cost-and-usage \
  --time-period Start=2023-01-01,End=2023-01-31 \
  --granularity MONTHLY \
  --metrics BlendedCost
```

### 10. **Prevention Strategies**

#### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.77.0
    hooks:
      - id: terraform_fmt
      - id: terraform_validate
      - id: terraform_tflint
      - id: terragrunt_validate
```

#### Automated Testing
```bash
# Run tests before deployment
go test -v ./test/

# Validate all configurations
find . -name "*.tf" -exec terraform validate {} \;
```

#### Change Management
```bash
# Always plan before apply
terraform plan -out=tfplan
# Review plan carefully
terraform show tfplan
# Apply only after approval
terraform apply tfplan
```

## 🚨 Emergency Procedures

### Critical Infrastructure Down
1. **Assess Impact**: Identify affected resources
2. **Check State**: Verify state file integrity
3. **Quick Fix**: Apply minimal changes to restore service
4. **Full Recovery**: Plan comprehensive fix
5. **Post-Mortem**: Document lessons learned

### State File Corruption
1. **Stop Operations**: Prevent further damage
2. **Backup Current State**: Even if corrupted
3. **Restore from Backup**: Use latest known good state
4. **Validate Restoration**: Compare with actual infrastructure
5. **Resume Operations**: After validation

### Security Breach
1. **Rotate Credentials**: All AWS keys and secrets
2. **Review Access Logs**: Check for unauthorized changes
3. **Audit Infrastructure**: Verify no malicious changes
4. **Update Security**: Strengthen access controls
5. **Monitor**: Enhanced monitoring for period

## 🔧 Useful Commands Reference

```bash
# Terraform
terraform init -upgrade
terraform plan -refresh=false
terraform apply -auto-approve
terraform destroy -auto-approve
terraform state list
terraform state show <resource>
terraform import <resource> <id>
terraform taint <resource>
terraform untaint <resource>

# Terragrunt
terragrunt run-all init
terragrunt run-all plan
terragrunt run-all apply
terragrunt run-all destroy
terragrunt dag graph
terragrunt validate
terragrunt hcl format

# AWS CLI
aws sts get-caller-identity
aws ec2 describe-instances
aws s3 ls s3://bucket-name
aws logs describe-log-groups
```

## 🔗 Next Steps
- **08-performance-optimization**: Speed up your deployments
- **09-security-hardening**: Secure your infrastructure
