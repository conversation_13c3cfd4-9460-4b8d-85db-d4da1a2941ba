# ☁️ AWS Provider Deep Dive - 1500+ Resources Mastery

## 🎯 AWS Provider Overview
The AWS provider offers **1500+ resources** and **606+ data sources** for comprehensive AWS infrastructure management.

## 🏗️ Core Service Categories

### 1. **Compute Services**
#### EC2 (Elastic Compute Cloud)
```hcl
# EC2 Instance
resource "aws_instance" "web" {
  ami                    = data.aws_ami.ubuntu.id
  instance_type          = "t3.micro"
  key_name              = aws_key_pair.deployer.key_name
  vpc_security_group_ids = [aws_security_group.web.id]
  subnet_id             = aws_subnet.public.id
  
  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    app_name = var.app_name
  }))
  
  tags = {
    Name = "web-server"
    Type = "application"
  }
}

# Auto Scaling Group
resource "aws_autoscaling_group" "web" {
  name                = "web-asg"
  vpc_zone_identifier = [aws_subnet.private.id]
  target_group_arns   = [aws_lb_target_group.web.arn]
  health_check_type   = "ELB"
  
  min_size         = 1
  max_size         = 10
  desired_capacity = 2
  
  launch_template {
    id      = aws_launch_template.web.id
    version = "$Latest"
  }
}
```

#### Lambda Functions
```hcl
resource "aws_lambda_function" "processor" {
  filename         = "lambda_function.zip"
  function_name    = "data-processor"
  role            = aws_iam_role.lambda_role.arn
  handler         = "index.handler"
  runtime         = "python3.9"
  timeout         = 30
  memory_size     = 256
  
  environment {
    variables = {
      BUCKET_NAME = aws_s3_bucket.data.bucket
      TABLE_NAME  = aws_dynamodb_table.data.name
    }
  }
}
```

### 2. **Networking Services**
#### VPC (Virtual Private Cloud)
```hcl
# VPC
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true
  
  tags = {
    Name = "main-vpc"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id
  
  tags = {
    Name = "main-igw"
  }
}

# NAT Gateway
resource "aws_nat_gateway" "main" {
  allocation_id = aws_eip.nat.id
  subnet_id     = aws_subnet.public.id
  
  tags = {
    Name = "main-nat"
  }
  
  depends_on = [aws_internet_gateway.main]
}
```

#### Load Balancers
```hcl
# Application Load Balancer
resource "aws_lb" "main" {
  name               = "main-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets           = [aws_subnet.public_a.id, aws_subnet.public_b.id]
  
  enable_deletion_protection = false
  
  tags = {
    Environment = "production"
  }
}

# Target Group
resource "aws_lb_target_group" "web" {
  name     = "web-tg"
  port     = 80
  protocol = "HTTP"
  vpc_id   = aws_vpc.main.id
  
  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/health"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }
}
```

### 3. **Storage Services**
#### S3 (Simple Storage Service)
```hcl
# S3 Bucket
resource "aws_s3_bucket" "data" {
  bucket = "my-data-bucket-${random_id.bucket_suffix.hex}"
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "data" {
  bucket = aws_s3_bucket.data.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket Encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "data" {
  bucket = aws_s3_bucket.data.id
  
  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.s3.arn
      sse_algorithm     = "aws:kms"
    }
  }
}
```

### 4. **Database Services**
#### RDS (Relational Database Service)
```hcl
# RDS Instance
resource "aws_db_instance" "main" {
  identifier     = "main-database"
  engine         = "postgres"
  engine_version = "13.7"
  instance_class = "db.t3.micro"
  
  allocated_storage     = 20
  max_allocated_storage = 100
  storage_type         = "gp2"
  storage_encrypted    = true
  
  db_name  = "maindb"
  username = "dbadmin"
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = true
  deletion_protection = false
  
  tags = {
    Name = "main-database"
  }
}
```

#### DynamoDB
```hcl
resource "aws_dynamodb_table" "users" {
  name           = "users"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "user_id"
  range_key      = "timestamp"
  
  attribute {
    name = "user_id"
    type = "S"
  }
  
  attribute {
    name = "timestamp"
    type = "N"
  }
  
  global_secondary_index {
    name     = "email-index"
    hash_key = "email"
    
    attribute {
      name = "email"
      type = "S"
    }
  }
  
  tags = {
    Name = "users-table"
  }
}
```

### 5. **Security Services**
#### IAM (Identity and Access Management)
```hcl
# IAM Role
resource "aws_iam_role" "lambda_role" {
  name = "lambda-execution-role"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# IAM Policy
resource "aws_iam_policy" "lambda_policy" {
  name        = "lambda-policy"
  description = "Policy for Lambda function"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject"
        ]
        Resource = "${aws_s3_bucket.data.arn}/*"
      }
    ]
  })
}
```

## 🎯 Advanced Patterns

### Data Sources Usage
```hcl
# Get current AWS account ID
data "aws_caller_identity" "current" {}

# Get available AZs
data "aws_availability_zones" "available" {
  state = "available"
}

# Get latest AMI
data "aws_ami" "ubuntu" {
  most_recent = true
  owners      = ["************"]
  
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*"]
  }
}
```

### Resource Dependencies
```hcl
# Explicit dependency
resource "aws_instance" "web" {
  # ... configuration ...
  depends_on = [aws_security_group.web]
}

# Implicit dependency through references
resource "aws_instance" "web" {
  subnet_id = aws_subnet.public.id  # Creates implicit dependency
}
```

## 📚 Service Categories Reference

### **Compute**: EC2, Lambda, ECS, EKS, Batch, Lightsail
### **Storage**: S3, EBS, EFS, FSx, Storage Gateway
### **Database**: RDS, DynamoDB, ElastiCache, DocumentDB, Neptune
### **Networking**: VPC, CloudFront, Route53, API Gateway, Direct Connect
### **Security**: IAM, KMS, Secrets Manager, Certificate Manager, WAF
### **Monitoring**: CloudWatch, X-Ray, Config, CloudTrail
### **Analytics**: Kinesis, EMR, Glue, Athena, QuickSight
### **Machine Learning**: SageMaker, Comprehend, Rekognition
### **DevOps**: CodePipeline, CodeBuild, CodeDeploy, CodeCommit

## 🔗 Next Steps
- **03-terragrunt-fundamentals**: Learn DRY configurations
- **04-advanced-patterns**: Complex multi-service architectures
