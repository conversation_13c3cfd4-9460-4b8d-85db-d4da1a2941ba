# 🏗️ Real-World Projects - Production-Ready Examples

## 🎯 Complete Infrastructure Scenarios

### Project 1: **Multi-Tier Web Application**
A production-ready 3-tier web application with high availability, security, and monitoring.

#### Architecture Overview
```
Internet Gateway
    ↓
Application Load Balancer (Public Subnets)
    ↓
Auto Scaling Group (Private Subnets)
    ↓
RDS Multi-AZ (Database Subnets)
```

#### Project Structure
```
web-application/
├── terragrunt.hcl                    # Root configuration
├── modules/                          # Reusable modules
│   ├── vpc/
│   ├── security-groups/
│   ├── alb/
│   ├── asg/
│   └── rds/
├── environments/
│   ├── dev/
│   │   ├── terragrunt.hcl
│   │   ├── vpc/terragrunt.hcl
│   │   ├── security/terragrunt.hcl
│   │   ├── alb/terragrunt.hcl
│   │   ├── app/terragrunt.hcl
│   │   └── database/terragrunt.hcl
│   ├── staging/
│   └── prod/
└── scripts/
    ├── deploy.sh
    └── destroy.sh
```

### Project 2: **Microservices on EKS**
Container orchestration with Kubernetes, service mesh, and observability.

#### Architecture Components
- **EKS Cluster** with managed node groups
- **Application Load Balancer** for ingress
- **Service Mesh** (Istio) for service communication
- **Monitoring Stack** (Prometheus, Grafana)
- **Logging** (ELK Stack)

### Project 3: **Data Lake Architecture**
Big data processing pipeline with S3, Glue, Athena, and QuickSight.

#### Data Flow
```
Data Sources → S3 Raw → Glue ETL → S3 Processed → Athena → QuickSight
```

### Project 4: **Serverless Application**
Event-driven architecture using Lambda, API Gateway, and DynamoDB.

#### Event Flow
```
API Gateway → Lambda → DynamoDB
     ↓
CloudWatch Events → Lambda → SQS → Lambda
```

## 🚀 Project 1 Deep Dive: Multi-Tier Web Application

### Root Configuration
```hcl
# terragrunt.hcl
remote_state {
  backend = "s3"
  config = {
    bucket         = "terraform-state-${get_aws_account_id()}"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "us-west-2"
    encrypt        = true
    dynamodb_table = "terraform-locks"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

generate "provider" {
  path = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents = <<EOF
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = var.project_name
      Environment = var.environment
      ManagedBy   = "Terragrunt"
    }
  }
}
EOF
}

inputs = {
  project_name = "web-app"
  aws_region   = "us-west-2"
}
```

### Environment Configuration
```hcl
# environments/prod/terragrunt.hcl
include "root" {
  path = find_in_parent_folders()
}

inputs = {
  environment = "prod"
  
  # VPC Configuration
  vpc_cidr = "10.0.0.0/16"
  availability_zones = ["us-west-2a", "us-west-2b", "us-west-2c"]
  
  # Application Configuration
  instance_type = "t3.large"
  min_size = 2
  max_size = 10
  desired_capacity = 4
  
  # Database Configuration
  db_instance_class = "db.r5.xlarge"
  db_allocated_storage = 100
  db_multi_az = true
  
  # Feature Flags
  enable_monitoring = true
  enable_backup = true
  enable_encryption = true
}
```

### VPC Module
```hcl
# environments/prod/vpc/terragrunt.hcl
include "root" {
  path = find_in_parent_folders()
}

terraform {
  source = "../../../modules/vpc"
}

inputs = {
  name = "${local.env_vars.inputs.project_name}-${local.env_vars.inputs.environment}"
  cidr = local.env_vars.inputs.vpc_cidr
  
  azs = local.env_vars.inputs.availability_zones
  
  private_subnets = ["********/24", "********/24", "********/24"]
  public_subnets  = ["**********/24", "**********/24", "**********/24"]
  database_subnets = ["**********/24", "**********/24", "**********/24"]
  
  enable_nat_gateway = true
  enable_vpn_gateway = false
  enable_dns_hostnames = true
  enable_dns_support = true
  
  tags = {
    Name = "${local.env_vars.inputs.project_name}-${local.env_vars.inputs.environment}-vpc"
  }
}

locals {
  env_vars = read_terragrunt_config(find_in_parent_folders("terragrunt.hcl"))
}
```

### Application Load Balancer
```hcl
# environments/prod/alb/terragrunt.hcl
include "root" {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
  mock_outputs = {
    vpc_id = "vpc-fake"
    public_subnets = ["subnet-fake-1", "subnet-fake-2"]
  }
}

dependency "security" {
  config_path = "../security"
  mock_outputs = {
    alb_security_group_id = "sg-fake"
  }
}

terraform {
  source = "../../../modules/alb"
}

inputs = {
  name = "${local.env_vars.inputs.project_name}-${local.env_vars.inputs.environment}"
  
  vpc_id = dependency.vpc.outputs.vpc_id
  subnets = dependency.vpc.outputs.public_subnets
  security_groups = [dependency.security.outputs.alb_security_group_id]
  
  enable_deletion_protection = true
  
  target_groups = [
    {
      name     = "web-servers"
      port     = 80
      protocol = "HTTP"
      health_check = {
        enabled             = true
        healthy_threshold   = 2
        interval            = 30
        matcher             = "200"
        path                = "/health"
        port                = "traffic-port"
        protocol            = "HTTP"
        timeout             = 5
        unhealthy_threshold = 2
      }
    }
  ]
  
  listeners = [
    {
      port     = "80"
      protocol = "HTTP"
      action_type = "redirect"
      redirect = {
        port        = "443"
        protocol    = "HTTPS"
        status_code = "HTTP_301"
      }
    },
    {
      port            = "443"
      protocol        = "HTTPS"
      certificate_arn = var.ssl_certificate_arn
      action_type     = "forward"
    }
  ]
}

locals {
  env_vars = read_terragrunt_config(find_in_parent_folders("terragrunt.hcl"))
}
```

### Auto Scaling Group
```hcl
# environments/prod/app/terragrunt.hcl
include "root" {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
  mock_outputs = {
    vpc_id = "vpc-fake"
    private_subnets = ["subnet-fake-1", "subnet-fake-2"]
  }
}

dependency "security" {
  config_path = "../security"
  mock_outputs = {
    app_security_group_id = "sg-fake"
  }
}

dependency "alb" {
  config_path = "../alb"
  mock_outputs = {
    target_group_arns = ["arn:aws:elasticloadbalancing:fake"]
  }
}

dependency "database" {
  config_path = "../database"
  mock_outputs = {
    db_endpoint = "fake.rds.amazonaws.com"
    db_port = 5432
  }
}

terraform {
  source = "../../../modules/asg"
}

inputs = {
  name = "${local.env_vars.inputs.project_name}-${local.env_vars.inputs.environment}"
  
  # Launch Template
  image_id      = data.aws_ami.amazon_linux.id
  instance_type = local.env_vars.inputs.instance_type
  key_name      = var.key_pair_name
  
  vpc_security_group_ids = [dependency.security.outputs.app_security_group_id]
  
  # Auto Scaling
  vpc_zone_identifier = dependency.vpc.outputs.private_subnets
  target_group_arns   = dependency.alb.outputs.target_group_arns
  
  min_size         = local.env_vars.inputs.min_size
  max_size         = local.env_vars.inputs.max_size
  desired_capacity = local.env_vars.inputs.desired_capacity
  
  # User Data
  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    db_endpoint = dependency.database.outputs.db_endpoint
    db_port     = dependency.database.outputs.db_port
    environment = local.env_vars.inputs.environment
  }))
  
  # Scaling Policies
  scaling_policies = [
    {
      name                   = "scale-up"
      scaling_adjustment     = 2
      adjustment_type        = "ChangeInCapacity"
      cooldown              = 300
      metric_aggregation_type = "Average"
    },
    {
      name                   = "scale-down"
      scaling_adjustment     = -1
      adjustment_type        = "ChangeInCapacity"
      cooldown              = 300
      metric_aggregation_type = "Average"
    }
  ]
}

locals {
  env_vars = read_terragrunt_config(find_in_parent_folders("terragrunt.hcl"))
}
```

### RDS Database
```hcl
# environments/prod/database/terragrunt.hcl
include "root" {
  path = find_in_parent_folders()
}

dependency "vpc" {
  config_path = "../vpc"
  mock_outputs = {
    vpc_id = "vpc-fake"
    database_subnets = ["subnet-fake-1", "subnet-fake-2"]
  }
}

dependency "security" {
  config_path = "../security"
  mock_outputs = {
    rds_security_group_id = "sg-fake"
  }
}

terraform {
  source = "../../../modules/rds"
}

inputs = {
  identifier = "${local.env_vars.inputs.project_name}-${local.env_vars.inputs.environment}"
  
  # Engine Configuration
  engine         = "postgres"
  engine_version = "13.7"
  instance_class = local.env_vars.inputs.db_instance_class
  
  # Storage Configuration
  allocated_storage     = local.env_vars.inputs.db_allocated_storage
  max_allocated_storage = local.env_vars.inputs.db_allocated_storage * 2
  storage_type         = "gp2"
  storage_encrypted    = local.env_vars.inputs.enable_encryption
  
  # Database Configuration
  db_name  = "webapp"
  username = "dbadmin"
  password = var.db_password  # From AWS Secrets Manager
  
  # Network Configuration
  vpc_security_group_ids = [dependency.security.outputs.rds_security_group_id]
  db_subnet_group_name   = dependency.vpc.outputs.database_subnet_group
  
  # High Availability
  multi_az = local.env_vars.inputs.db_multi_az
  
  # Backup Configuration
  backup_retention_period = local.env_vars.inputs.enable_backup ? 7 : 0
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  # Monitoring
  monitoring_interval = local.env_vars.inputs.enable_monitoring ? 60 : 0
  monitoring_role_arn = local.env_vars.inputs.enable_monitoring ? aws_iam_role.rds_enhanced_monitoring.arn : null
  
  # Security
  deletion_protection = true
  skip_final_snapshot = false
  final_snapshot_identifier = "${local.env_vars.inputs.project_name}-${local.env_vars.inputs.environment}-final-snapshot"
}

locals {
  env_vars = read_terragrunt_config(find_in_parent_folders("terragrunt.hcl"))
}
```

## 🚀 Deployment Scripts

### Automated Deployment
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

ENVIRONMENT=${1:-dev}
ACTION=${2:-plan}

echo "🚀 Deploying $ENVIRONMENT environment..."

cd "environments/$ENVIRONMENT"

case $ACTION in
  "plan")
    echo "📋 Planning infrastructure..."
    terragrunt run-all plan
    ;;
  "apply")
    echo "🏗️ Applying infrastructure..."
    terragrunt run-all apply
    ;;
  "destroy")
    echo "💥 Destroying infrastructure..."
    terragrunt run-all destroy
    ;;
  *)
    echo "❌ Invalid action. Use: plan, apply, or destroy"
    exit 1
    ;;
esac

echo "✅ $ACTION completed for $ENVIRONMENT environment"
```

### CI/CD Pipeline
```yaml
# .github/workflows/terraform.yml
name: Terraform

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  terraform:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        environment: [dev, staging, prod]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Terragrunt
      uses: autero1/action-terragrunt@v1.1.0
      with:
        terragrunt_version: 0.45.0
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    
    - name: Terragrunt Plan
      run: |
        cd environments/${{ matrix.environment }}
        terragrunt run-all plan
    
    - name: Terragrunt Apply
      if: github.ref == 'refs/heads/main' && matrix.environment == 'prod'
      run: |
        cd environments/${{ matrix.environment }}
        terragrunt run-all apply -auto-approve
```

## 🔗 Next Steps
- **06-best-practices**: Industry standards and guidelines
- **08-performance-optimization**: Speed optimization techniques
