# 🔧 Terragrunt Fundamentals - DRY Infrastructure Orchestration

## 🎯 What is Terragrunt?

Terragrunt is a thin wrapper for Terraform that provides extra tools for:
- **DRY configurations** (Don't Repeat Yourself)
- **Remote state management**
- **Dependency management**
- **Multi-environment deployments**
- **Stack orchestration**

## 🏗️ Core Concepts

### 1. **Units vs Stacks**
- **Unit**: A single Terraform configuration (one `terragrunt.hcl`)
- **Stack**: A collection of units that work together
- **DAG**: Directed Acyclic Graph of dependencies

### 2. **Configuration Hierarchy**
```
infrastructure/
├── terragrunt.hcl              # Root configuration
├── dev/
│   ├── terragrunt.hcl         # Environment config
│   ├── vpc/
│   │   └── terragrunt.hcl     # Unit config
│   └── app/
│       └── terragrunt.hcl     # Unit config
└── prod/
    ├── terragrunt.hcl         # Environment config
    ├── vpc/
    │   └── terragrunt.hcl     # Unit config
    └── app/
        └── terragrunt.hcl     # Unit config
```

### 3. **Basic terragrunt.hcl Structure**
```hcl
# Include parent configuration
include "root" {
  path = find_in_parent_folders()
}

# Terraform module source
terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-vpc.git?ref=v3.14.0"
}

# Input variables
inputs = {
  name = "my-vpc"
  cidr = "10.0.0.0/16"
  
  azs             = ["us-west-2a", "us-west-2b", "us-west-2c"]
  private_subnets = ["10.0.1.0/24", "10.0.2.0/24", "10.0.3.0/24"]
  public_subnets  = ["10.0.101.0/24", "10.0.102.0/24", "10.0.103.0/24"]
  
  enable_nat_gateway = true
  enable_vpn_gateway = true
  
  tags = {
    Terraform   = "true"
    Environment = "dev"
  }
}
```

## 🔄 Remote State Management

### Root Configuration (terragrunt.hcl)
```hcl
# Configure remote state
remote_state {
  backend = "s3"
  config = {
    bucket         = "my-terraform-state-${get_aws_account_id()}"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "us-west-2"
    encrypt        = true
    dynamodb_table = "terraform-locks"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}

# Generate provider configuration
generate "provider" {
  path = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents = <<EOF
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = "us-west-2"
  
  default_tags {
    tags = {
      Terraform   = "true"
      Environment = var.environment
      Project     = var.project_name
    }
  }
}
EOF
}

# Common inputs for all environments
inputs = {
  project_name = "my-project"
  region       = "us-west-2"
}
```

## 🔗 Dependency Management

### Dependencies Between Units
```hcl
# app/terragrunt.hcl
include "root" {
  path = find_in_parent_folders()
}

# Depend on VPC unit
dependency "vpc" {
  config_path = "../vpc"
  
  # Mock outputs for planning
  mock_outputs = {
    vpc_id = "vpc-fake-id"
    private_subnet_ids = ["subnet-fake-1", "subnet-fake-2"]
  }
  mock_outputs_allowed_terraform_commands = ["validate", "plan"]
}

terraform {
  source = "../../modules/app"
}

inputs = {
  vpc_id     = dependency.vpc.outputs.vpc_id
  subnet_ids = dependency.vpc.outputs.private_subnet_ids
  
  app_name = "my-app"
  environment = "dev"
}
```

## 🎯 Built-in Functions

### Path Functions
```hcl
# Find parent terragrunt.hcl
include "root" {
  path = find_in_parent_folders()
}

# Get current directory relative to root
inputs = {
  environment = path_relative_to_include()
}

# Get repository root
inputs = {
  repo_root = get_repo_root()
}
```

### AWS Functions
```hcl
inputs = {
  account_id = get_aws_account_id()
  caller_arn = get_aws_caller_identity_arn()
  region     = get_env("AWS_REGION", "us-west-2")
}
```

### Environment Functions
```hcl
inputs = {
  environment = get_env("ENVIRONMENT", "dev")
  debug_mode  = get_env("DEBUG", "false")
}
```

## 🚀 Stack Operations

### Running Commands on Stacks
```bash
# Run on all units in current directory and subdirectories
terragrunt run-all plan
terragrunt run-all apply
terragrunt run-all destroy

# New stack commands (recommended)
terragrunt stack run plan
terragrunt stack run apply
terragrunt stack run destroy

# Run specific command
terragrunt run-all validate
terragrunt run-all fmt
```

### Dependency Order
Terragrunt automatically determines the order based on dependencies:
1. **VPC** (no dependencies)
2. **Security Groups** (depends on VPC)
3. **Database** (depends on VPC, Security Groups)
4. **Application** (depends on VPC, Database)

## 🔧 Advanced Features

### Hooks (Before/After Actions)
```hcl
terraform {
  before_hook "validate_terraform" {
    commands = ["plan", "apply"]
    execute  = ["terraform", "validate"]
  }
  
  after_hook "notify_slack" {
    commands = ["apply"]
    execute  = ["./scripts/notify-slack.sh", "Applied ${path_relative_to_include()}"]
  }
  
  error_hook "cleanup" {
    commands = ["apply"]
    execute  = ["./scripts/cleanup.sh"]
  }
}
```

### Generate Blocks
```hcl
# Generate versions.tf
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents = <<EOF
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}
EOF
}
```

### Extra Arguments
```hcl
terraform {
  extra_arguments "common_vars" {
    commands = ["plan", "apply"]
    arguments = [
      "-var-file=${get_terragrunt_dir()}/../../common.tfvars"
    ]
  }
  
  extra_arguments "disable_input" {
    commands  = ["plan", "apply"]
    arguments = ["-input=false"]
  }
}
```

## 📁 Directory Structure Best Practices

### Option 1: Environment-First
```
infrastructure/
├── terragrunt.hcl
├── dev/
│   ├── terragrunt.hcl
│   ├── vpc/terragrunt.hcl
│   ├── rds/terragrunt.hcl
│   └── app/terragrunt.hcl
├── staging/
│   ├── terragrunt.hcl
│   ├── vpc/terragrunt.hcl
│   ├── rds/terragrunt.hcl
│   └── app/terragrunt.hcl
└── prod/
    ├── terragrunt.hcl
    ├── vpc/terragrunt.hcl
    ├── rds/terragrunt.hcl
    └── app/terragrunt.hcl
```

### Option 2: Service-First
```
infrastructure/
├── terragrunt.hcl
├── vpc/
│   ├── dev/terragrunt.hcl
│   ├── staging/terragrunt.hcl
│   └── prod/terragrunt.hcl
├── rds/
│   ├── dev/terragrunt.hcl
│   ├── staging/terragrunt.hcl
│   └── prod/terragrunt.hcl
└── app/
    ├── dev/terragrunt.hcl
    ├── staging/terragrunt.hcl
    └── prod/terragrunt.hcl
```

## 🎯 Common Commands

```bash
# Initialize and plan
terragrunt init
terragrunt plan

# Apply changes
terragrunt apply

# Work with stacks
terragrunt stack run plan
terragrunt stack run apply

# Validate configurations
terragrunt validate
terragrunt hcl validate

# Format code
terragrunt hcl format

# Show dependency graph
terragrunt dag graph

# List all units
terragrunt list

# Find specific units
terragrunt find --filter="vpc"
```

## 🔗 Next Steps
- **04-advanced-patterns**: Complex multi-environment scenarios
- **05-real-world-projects**: Production-ready examples
