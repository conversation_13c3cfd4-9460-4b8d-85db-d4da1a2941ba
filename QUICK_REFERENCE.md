# 🚀 Terraform & Terragrunt Quick Reference

## 📋 Essential Commands

### Terraform Commands
```bash
# Initialization and Setup
terraform init                    # Initialize working directory
terraform init -upgrade           # Upgrade providers to latest versions
terraform init -reconfigure       # Reconfigure backend

# Planning and Validation
terraform validate                # Validate configuration syntax
terraform fmt                     # Format code
terraform fmt -check              # Check if code is formatted
terraform plan                    # Preview changes
terraform plan -out=tfplan        # Save plan to file
terraform plan -destroy           # Plan destruction

# Applying Changes
terraform apply                   # Apply changes
terraform apply tfplan            # Apply saved plan
terraform apply -auto-approve     # Apply without confirmation
terraform destroy                 # Destroy infrastructure
terraform destroy -auto-approve   # Destroy without confirmation

# State Management
terraform state list              # List resources in state
terraform state show <resource>   # Show resource details
terraform state mv <old> <new>    # Move resource in state
terraform state rm <resource>     # Remove resource from state
terraform import <resource> <id>  # Import existing resource
terraform refresh                 # Update state with real infrastructure

# Outputs and Information
terraform output                  # Show all outputs
terraform output <name>           # Show specific output
terraform show                    # Show current state
terraform version                 # Show version information
```

### Terragrunt Commands
```bash
# Basic Operations
terragrunt init                   # Initialize Terragrunt unit
terragrunt plan                   # Plan single unit
terragrunt apply                  # Apply single unit
terragrunt destroy                # Destroy single unit

# Stack Operations (New CLI)
terragrunt stack run init         # Initialize all units in stack
terragrunt stack run plan         # Plan all units in stack
terragrunt stack run apply        # Apply all units in stack
terragrunt stack run destroy      # Destroy all units in stack

# Legacy Stack Operations (Still supported)
terragrunt run-all init           # Initialize all units
terragrunt run-all plan           # Plan all units
terragrunt run-all apply          # Apply all units
terragrunt run-all destroy        # Destroy all units

# Configuration Management
terragrunt validate               # Validate Terragrunt configuration
terragrunt hcl validate          # Validate HCL syntax
terragrunt hcl format            # Format HCL files
terragrunt hcl format --check    # Check HCL formatting

# Discovery and Analysis
terragrunt list                   # List all units
terragrunt find                   # Find units with filters
terragrunt dag graph              # Show dependency graph
terragrunt dag graph | dot -Tpng > deps.png  # Generate graph image

# Information and Debugging
terragrunt info                   # Show unit information
terragrunt render                 # Render final configuration
terragrunt output                 # Show outputs from dependencies
```

## 🔧 Configuration Patterns

### Basic terragrunt.hcl Structure
```hcl
# Include parent configuration
include "root" {
  path = find_in_parent_folders()
}

# Terraform module source
terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-vpc.git?ref=v3.14.0"
}

# Dependencies
dependency "vpc" {
  config_path = "../vpc"
  mock_outputs = {
    vpc_id = "vpc-fake"
  }
}

# Input variables
inputs = {
  name = "my-vpc"
  cidr = "10.0.0.0/16"
  vpc_id = dependency.vpc.outputs.vpc_id
}
```

### Remote State Configuration
```hcl
remote_state {
  backend = "s3"
  config = {
    bucket         = "terraform-state-${get_aws_account_id()}"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "us-west-2"
    encrypt        = true
    dynamodb_table = "terraform-locks"
  }
  generate = {
    path      = "backend.tf"
    if_exists = "overwrite_terragrunt"
  }
}
```

### Provider Generation
```hcl
generate "provider" {
  path = "provider.tf"
  if_exists = "overwrite_terragrunt"
  contents = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}
EOF
}
```

## 🎯 Common Terraform Patterns

### Resource with Lifecycle Rules
```hcl
resource "aws_instance" "web" {
  ami           = data.aws_ami.ubuntu.id
  instance_type = var.instance_type
  
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
    ignore_changes       = [ami, user_data]
  }
  
  tags = {
    Name = "web-server"
  }
}
```

### Conditional Resources
```hcl
resource "aws_instance" "web" {
  count = var.create_instance ? 1 : 0
  
  ami           = data.aws_ami.ubuntu.id
  instance_type = var.instance_type
}

# Or with for_each
resource "aws_instance" "web" {
  for_each = var.instances
  
  ami           = data.aws_ami.ubuntu.id
  instance_type = each.value.instance_type
  
  tags = {
    Name = each.key
  }
}
```

### Data Sources
```hcl
data "aws_ami" "ubuntu" {
  most_recent = true
  owners      = ["099720109477"]
  
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*"]
  }
}

data "aws_caller_identity" "current" {}
data "aws_region" "current" {}
data "aws_availability_zones" "available" {}
```

## 🔍 Debugging Commands

### Terraform Debugging
```bash
# Enable debug logging
export TF_LOG=DEBUG
export TF_LOG_PATH=terraform.log

# Validate and check syntax
terraform validate
terraform fmt -check -recursive

# Plan with detailed output
terraform plan -detailed-exitcode
terraform plan -refresh=false

# Show state and resources
terraform state list
terraform state show aws_instance.web
terraform show
```

### Terragrunt Debugging
```bash
# Debug logging
terragrunt plan --terragrunt-log-level debug

# Validate configuration
terragrunt validate
terragrunt hcl validate

# Check dependencies
terragrunt dag graph
terragrunt list

# Render final configuration
terragrunt render
```

## 🚨 Emergency Commands

### Force Unlock State
```bash
# Get lock ID from error message
terraform force-unlock LOCK_ID

# For Terragrunt
terragrunt force-unlock LOCK_ID
```

### Import Existing Resources
```bash
# Import resource into state
terraform import aws_instance.web i-1234567890abcdef0

# For Terragrunt
terragrunt import aws_instance.web i-1234567890abcdef0
```

### State Recovery
```bash
# Backup current state
terraform state pull > backup.tfstate

# Restore from backup
terraform state push backup.tfstate

# Remove resource from state
terraform state rm aws_instance.web
```

## 📊 Useful Functions

### Terragrunt Built-in Functions
```hcl
# Path functions
find_in_parent_folders()
path_relative_to_include()
get_repo_root()
get_terragrunt_dir()

# AWS functions
get_aws_account_id()
get_aws_caller_identity_arn()
get_aws_region()

# Environment functions
get_env("ENVIRONMENT", "dev")
get_platform()

# Configuration functions
read_terragrunt_config("../env.hcl")
```

### Terraform Functions
```hcl
# String functions
format("Hello %s", var.name)
join(",", var.list)
split(",", var.string)

# Collection functions
length(var.list)
keys(var.map)
values(var.map)
lookup(var.map, "key", "default")

# Encoding functions
base64encode(var.string)
jsonencode(var.object)
yamlencode(var.object)

# Filesystem functions
file("${path.module}/script.sh")
templatefile("${path.module}/template.tpl", var.vars)

# Network functions
cidrsubnet("10.0.0.0/16", 8, 1)
cidrhost("10.0.0.0/24", 5)
```

## 🎯 Performance Tips

### Speed Up Operations
```bash
# Use provider caching
export TF_PLUGIN_CACHE_DIR="$HOME/.terraform.d/plugin-cache"

# Increase parallelism
terraform apply -parallelism=10
terragrunt run-all apply --terragrunt-parallelism 5

# Skip refresh for planning
terraform plan -refresh=false

# Use partial configuration
terragrunt plan --terragrunt-use-partial-parse-config-cache
```

### Optimize Dependencies
```hcl
# Use mock outputs
dependency "vpc" {
  config_path = "../vpc"
  mock_outputs = {
    vpc_id = "vpc-fake"
  }
  mock_outputs_allowed_terraform_commands = ["validate", "plan"]
}

# Skip dependency inputs
terragrunt plan --terragrunt-skip-dependencies-inputs
```

---

*Keep this reference handy for quick lookups! 📚*
