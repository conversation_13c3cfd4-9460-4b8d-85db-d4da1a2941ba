# 📋 Terraform & Terragrunt Best Practices

## 🎯 Industry Standards & Guidelines

### 1. **Code Organization & Structure**

#### Directory Structure Standards
```
infrastructure/
├── .gitignore                    # Ignore sensitive files
├── README.md                     # Project documentation
├── terragrunt.hcl               # Root configuration
├── modules/                     # Reusable modules
│   ├── vpc/
│   ├── security-groups/
│   └── application/
├── environments/
│   ├── dev/
│   ├── staging/
│   └── prod/
├── scripts/                     # Helper scripts
├── docs/                        # Additional documentation
└── tests/                       # Automated tests
```

#### Naming Conventions
```hcl
# Resource naming pattern: {environment}-{service}-{resource-type}
resource "aws_instance" "web" {
  tags = {
    Name = "${var.environment}-${var.service_name}-web-server"
  }
}

# Variable naming: descriptive and consistent
variable "vpc_cidr_block" {
  description = "CIDR block for the VPC"
  type        = string
}

# Output naming: clear and descriptive
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}
```

### 2. **Security Best Practices**

#### Secrets Management
```hcl
# ❌ NEVER do this
variable "database_password" {
  default = "supersecret123"  # NEVER hardcode secrets
}

# ✅ Use AWS Secrets Manager
data "aws_secretsmanager_secret_version" "db_password" {
  secret_id = "prod/database/password"
}

# ✅ Use environment variables
variable "database_password" {
  description = "Database password from environment"
  type        = string
  sensitive   = true
}
```

#### IAM Principle of Least Privilege
```hcl
# ✅ Specific permissions only
resource "aws_iam_policy" "app_policy" {
  name = "app-specific-policy"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject"
        ]
        Resource = [
          "${aws_s3_bucket.app_data.arn}/*"
        ]
      }
    ]
  })
}

# ❌ Avoid overly broad permissions
# Action = "*"
# Resource = "*"
```

#### Encryption Standards
```hcl
# S3 Bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "app_data" {
  bucket = aws_s3_bucket.app_data.id
  
  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.app_data.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# RDS encryption
resource "aws_db_instance" "main" {
  storage_encrypted = true
  kms_key_id       = aws_kms_key.rds.arn
  # ... other configuration
}
```

### 3. **State Management Best Practices**

#### Remote State Configuration
```hcl
# terragrunt.hcl
remote_state {
  backend = "s3"
  config = {
    bucket         = "terraform-state-${get_aws_account_id()}"
    key            = "${path_relative_to_include()}/terraform.tfstate"
    region         = "us-west-2"
    encrypt        = true
    dynamodb_table = "terraform-locks"
    
    # Enable versioning for state recovery
    versioning = true
    
    # Server-side encryption
    server_side_encryption_configuration = {
      rule = {
        apply_server_side_encryption_by_default = {
          sse_algorithm = "AES256"
        }
      }
    }
  }
}
```

#### State File Security
```bash
# .gitignore
*.tfstate
*.tfstate.*
.terraform/
.terragrunt-cache/
*.tfvars
!example.tfvars
```

### 4. **Variable Management**

#### Variable Validation
```hcl
variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "t3.micro"
  
  validation {
    condition = contains([
      "t3.micro", "t3.small", "t3.medium", 
      "t3.large", "t3.xlarge"
    ], var.instance_type)
    error_message = "Instance type must be a valid t3 instance type."
  }
}

variable "environment" {
  description = "Environment name"
  type        = string
  
  validation {
    condition     = contains(["dev", "staging", "prod"], var.environment)
    error_message = "Environment must be dev, staging, or prod."
  }
}
```

#### Environment-Specific Variables
```hcl
# environments/dev/terragrunt.hcl
inputs = {
  environment = "dev"
  instance_type = "t3.micro"
  min_size = 1
  max_size = 2
  enable_monitoring = false
}

# environments/prod/terragrunt.hcl
inputs = {
  environment = "prod"
  instance_type = "t3.large"
  min_size = 2
  max_size = 10
  enable_monitoring = true
}
```

### 5. **Module Design Patterns**

#### Module Structure
```
modules/vpc/
├── main.tf          # Primary resources
├── variables.tf     # Input variables
├── outputs.tf       # Output values
├── versions.tf      # Provider requirements
├── README.md        # Module documentation
└── examples/        # Usage examples
    └── basic/
        ├── main.tf
        └── variables.tf
```

#### Module Versioning
```hcl
# Use specific module versions
terraform {
  source = "git::https://github.com/terraform-aws-modules/terraform-aws-vpc.git?ref=v3.14.0"
}

# Pin provider versions
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  required_version = ">= 1.0"
}
```

### 6. **Resource Tagging Strategy**

#### Consistent Tagging
```hcl
# Common tags for all resources
locals {
  common_tags = {
    Environment   = var.environment
    Project       = var.project_name
    Owner         = var.team_name
    CostCenter    = var.cost_center
    Terraform     = "true"
    CreatedBy     = "terragrunt"
    CreatedDate   = timestamp()
  }
}

# Apply to resources
resource "aws_instance" "web" {
  # ... configuration ...
  
  tags = merge(local.common_tags, {
    Name = "${var.environment}-web-server"
    Type = "web-server"
  })
}
```

#### Automated Tagging
```hcl
# Provider-level default tags
provider "aws" {
  default_tags {
    tags = {
      Environment = var.environment
      Project     = var.project_name
      Terraform   = "true"
    }
  }
}
```

### 7. **Error Handling & Validation**

#### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.77.0
    hooks:
      - id: terraform_fmt
      - id: terraform_validate
      - id: terraform_docs
      - id: terraform_tflint
      - id: terragrunt_validate
```

#### Terragrunt Validation
```hcl
# terragrunt.hcl
terraform {
  before_hook "validate" {
    commands = ["plan", "apply"]
    execute  = ["terraform", "validate"]
  }
  
  before_hook "fmt_check" {
    commands = ["plan", "apply"]
    execute  = ["terraform", "fmt", "-check"]
  }
}
```

### 8. **Performance Optimization**

#### Provider Caching
```bash
# Set up provider cache
export TF_PLUGIN_CACHE_DIR="$HOME/.terraform.d/plugin-cache"
mkdir -p $TF_PLUGIN_CACHE_DIR
```

#### Parallel Execution
```bash
# Optimize terragrunt runs
terragrunt run-all apply --terragrunt-parallelism 10
terragrunt run-all plan --terragrunt-parallelism 5
```

#### Dependency Optimization
```hcl
# Use mock outputs to avoid unnecessary dependencies
dependency "vpc" {
  config_path = "../vpc"
  
  mock_outputs = {
    vpc_id = "vpc-fake-id"
    private_subnet_ids = ["subnet-fake-1", "subnet-fake-2"]
  }
  mock_outputs_allowed_terraform_commands = ["validate", "plan", "destroy"]
}
```

### 9. **Documentation Standards**

#### Module Documentation
```hcl
# variables.tf
variable "vpc_cidr" {
  description = "CIDR block for VPC. Must be a valid IPv4 CIDR block."
  type        = string
  default     = "10.0.0.0/16"
  
  validation {
    condition     = can(cidrhost(var.vpc_cidr, 0))
    error_message = "VPC CIDR must be a valid IPv4 CIDR block."
  }
}

# outputs.tf
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}
```

#### README Templates
```markdown
# Module Name

## Description
Brief description of what this module does.

## Usage
```hcl
module "example" {
  source = "./modules/example"
  
  name = "my-example"
  environment = "dev"
}
```

## Requirements
| Name | Version |
|------|---------|
| terraform | >= 1.0 |
| aws | ~> 5.0 |

## Inputs
| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| name | Resource name | `string` | n/a | yes |

## Outputs
| Name | Description |
|------|-------------|
| id | Resource ID |
```

### 10. **Testing & Quality Assurance**

#### Automated Testing
```go
// test/vpc_test.go
func TestVPCModule(t *testing.T) {
    terraformOptions := &terraform.Options{
        TerraformDir: "../examples/vpc",
        Vars: map[string]interface{}{
            "vpc_cidr": "10.0.0.0/16",
            "name":     "test-vpc",
        },
    }
    
    defer terraform.Destroy(t, terraformOptions)
    terraform.InitAndApply(t, terraformOptions)
    
    vpcId := terraform.Output(t, terraformOptions, "vpc_id")
    assert.NotEmpty(t, vpcId)
}
```

#### Linting Configuration
```hcl
# .tflint.hcl
plugin "aws" {
  enabled = true
  version = "0.21.0"
  source  = "github.com/terraform-linters/tflint-ruleset-aws"
}

rule "aws_instance_invalid_type" {
  enabled = true
}

rule "aws_instance_previous_type" {
  enabled = true
}
```

## 🎯 Checklist for Production Readiness

### Security ✅
- [ ] No hardcoded secrets
- [ ] Proper IAM permissions (least privilege)
- [ ] Encryption at rest and in transit
- [ ] Network security (security groups, NACLs)
- [ ] Regular security scanning

### Reliability ✅
- [ ] Multi-AZ deployment
- [ ] Backup and disaster recovery
- [ ] Health checks and monitoring
- [ ] Auto-scaling configuration
- [ ] Error handling and retries

### Maintainability ✅
- [ ] Consistent naming conventions
- [ ] Proper documentation
- [ ] Module versioning
- [ ] Code reviews
- [ ] Automated testing

### Performance ✅
- [ ] Resource right-sizing
- [ ] Provider caching
- [ ] Parallel execution
- [ ] Dependency optimization
- [ ] Regular performance reviews

### Compliance ✅
- [ ] Tagging strategy
- [ ] Audit logging
- [ ] Compliance scanning
- [ ] Change management
- [ ] Access controls

## 🔗 Next Steps
- **07-troubleshooting**: Common issues and solutions
- **08-performance-optimization**: Advanced optimization techniques
