# IAM module variables

variable "environment" {
  description = "Environment name (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "entanglement"
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "aws_account_id" {
  description = "AWS account ID"
  type        = string
}

variable "ec2_instance_arns" {
  description = "List of EC2 instance ARNs for start/stop operations"
  type        = list(string)
  default = [
    "arn:aws:ec2:us-east-1:************:instance/i-0456aed95c9d1bd48",
    "arn:aws:ec2:us-east-1:************:instance/i-026c9c5455845cdb0"
  ]
}

variable "lambda_function_arns" {
  description = "Map of Lambda function ARNs"
  type = object({
    start_ec2 = string
    stop_ec2  = string
  })
  default = {
    start_ec2 = "arn:aws:lambda:us-east-1:************:function:StartInstancEC2"
    stop_ec2  = "arn:aws:lambda:us-east-1:************:function:StopInstanceEC2"
  }
}

variable "tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default     = {}
}
