# IAM module main configuration

# Local values for common tags and naming
locals {
  common_tags = merge(var.tags, {
    Environment = var.environment
    Project     = var.project_name
    ManagedBy   = "Terraform"
  })

  name_prefix = "${var.environment}-${var.project_name}"
}

# Data source for current AWS account ID
data "aws_caller_identity" "current" {}

# Data source for current AWS region
data "aws_region" "current" {}

#==============================================================================
# IAM POLICIES
#==============================================================================

# 1. EC2 Start/Stop Policy
resource "aws_iam_policy" "ec2_start_stop" {
  name        = "${local.name_prefix}-start-stop-ec2-instance-policy"
  description = "Policy to allow starting and stopping specific EC2 instances"
  path        = "/"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "VisualEditor0"
        Effect = "Allow"
        Action = [
          "ec2:StartInstances",
          "ec2:StopInstances"
        ]
        Resource = var.ec2_instance_arns
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-start-stop-ec2-instance-policy"
    Type = "EC2Management"
  })
}

# 2. EventBridge Scheduler Execution Policy - Start EC2
resource "aws_iam_policy" "eventbridge_start_ec2" {
  name        = "${local.name_prefix}-eventbridge-scheduler-execution-policy-start-ec2-instance"
  description = "Policy for EventBridge to invoke Lambda function for starting EC2 instances"
  path        = "/"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = [
          "${var.lambda_function_arns.start_ec2}:*",
          var.lambda_function_arns.start_ec2
        ]
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-eventbridge-start-ec2-policy"
    Type = "EventBridgeExecution"
  })
}

# 3. EventBridge Scheduler Execution Policy - Stop EC2
resource "aws_iam_policy" "eventbridge_stop_ec2" {
  name        = "${local.name_prefix}-eventbridge-scheduler-execution-policy-stop-ec2-instance"
  description = "Policy for EventBridge to invoke Lambda function for stopping EC2 instances"
  path        = "/"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = [
          "${var.lambda_function_arns.stop_ec2}:*",
          var.lambda_function_arns.stop_ec2
        ]
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-eventbridge-stop-ec2-policy"
    Type = "EventBridgeExecution"
  })
}

# 4. Cognito Publish Policy
resource "aws_iam_policy" "cognito_publish" {
  name        = "${local.name_prefix}-cognito-publish-policy"
  description = "Policy to allow publishing to SNS topics for Cognito"
  path        = "/"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sns:publish"
        ]
        Resource = ["*"]
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-cognito-publish-policy"
    Type = "CognitoSNS"
  })
}

# 5. ECR Access Policy
resource "aws_iam_policy" "ecr_access" {
  name        = "${local.name_prefix}-ecr-access-policy"
  description = "Policy to allow access to ECR repositories"
  path        = "/"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetAuthorizationToken"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-ecr-access-policy"
    Type = "ECRAccess"
  })
}

# 6. Kinesis Video Streams Access Policy
resource "aws_iam_policy" "kvs_access" {
  name        = "${local.name_prefix}-kvs-access-policy"
  description = "Policy to allow access to Kinesis Video Streams"
  path        = "/"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "kinesisvideo:*"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-kvs-access-policy"
    Type = "KinesisVideoStreams"
  })
}

#==============================================================================
# IAM ROLES
#==============================================================================

# Lambda Execution Role for EC2 Management
resource "aws_iam_role" "lambda_ec2_management" {
  name = "${local.name_prefix}-lambda-ec2-management-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-lambda-ec2-management-role"
    Type = "LambdaExecutionRole"
  })
}

# EventBridge Scheduler Role
resource "aws_iam_role" "eventbridge_scheduler" {
  name = "${local.name_prefix}-eventbridge-scheduler-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "scheduler.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-eventbridge-scheduler-role"
    Type = "EventBridgeSchedulerRole"
  })
}

# Cognito Service Role
resource "aws_iam_role" "cognito_service" {
  name = "${local.name_prefix}-cognito-service-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "cognito-idp.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-cognito-service-role"
    Type = "CognitoServiceRole"
  })
}

# EC2 Instance Role (for applications running on EC2)
resource "aws_iam_role" "ec2_application" {
  name = "${local.name_prefix}-ec2-application-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-ec2-application-role"
    Type = "EC2InstanceRole"
  })
}

#==============================================================================
# POLICY ATTACHMENTS
#==============================================================================

# Lambda EC2 Management Role Policy Attachments
resource "aws_iam_role_policy_attachment" "lambda_ec2_basic_execution" {
  role       = aws_iam_role.lambda_ec2_management.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "lambda_ec2_start_stop" {
  role       = aws_iam_role.lambda_ec2_management.name
  policy_arn = aws_iam_policy.ec2_start_stop.arn
}

# EventBridge Scheduler Role Policy Attachments
resource "aws_iam_role_policy_attachment" "eventbridge_start_ec2" {
  role       = aws_iam_role.eventbridge_scheduler.name
  policy_arn = aws_iam_policy.eventbridge_start_ec2.arn
}

resource "aws_iam_role_policy_attachment" "eventbridge_stop_ec2" {
  role       = aws_iam_role.eventbridge_scheduler.name
  policy_arn = aws_iam_policy.eventbridge_stop_ec2.arn
}

# Cognito Service Role Policy Attachments
resource "aws_iam_role_policy_attachment" "cognito_sns_publish" {
  role       = aws_iam_role.cognito_service.name
  policy_arn = aws_iam_policy.cognito_publish.arn
}

# EC2 Application Role Policy Attachments
resource "aws_iam_role_policy_attachment" "ec2_ecr_access" {
  role       = aws_iam_role.ec2_application.name
  policy_arn = aws_iam_policy.ecr_access.arn
}

resource "aws_iam_role_policy_attachment" "ec2_kvs_access" {
  role       = aws_iam_role.ec2_application.name
  policy_arn = aws_iam_policy.kvs_access.arn
}

# Optional: Attach CloudWatch Agent policy for EC2 monitoring
resource "aws_iam_role_policy_attachment" "ec2_cloudwatch_agent" {
  role       = aws_iam_role.ec2_application.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

# Optional: Attach SSM managed instance core for EC2 management
resource "aws_iam_role_policy_attachment" "ec2_ssm_managed_instance" {
  role       = aws_iam_role.ec2_application.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

#==============================================================================
# INSTANCE PROFILES
#==============================================================================

# EC2 Instance Profile
resource "aws_iam_instance_profile" "ec2_application" {
  name = "${local.name_prefix}-ec2-application-profile"
  role = aws_iam_role.ec2_application.name

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-ec2-application-profile"
    Type = "InstanceProfile"
  })
}
