# IAM module outputs

#==============================================================================
# POLICY OUTPUTS
#==============================================================================

output "policy_arns" {
  description = "Map of all policy ARNs created by this module"
  value = {
    ec2_start_stop      = aws_iam_policy.ec2_start_stop.arn
    eventbridge_start_ec2 = aws_iam_policy.eventbridge_start_ec2.arn
    eventbridge_stop_ec2  = aws_iam_policy.eventbridge_stop_ec2.arn
    cognito_publish     = aws_iam_policy.cognito_publish.arn
    ecr_access         = aws_iam_policy.ecr_access.arn
    kvs_access         = aws_iam_policy.kvs_access.arn
  }
}

output "ec2_start_stop_policy_arn" {
  description = "ARN of the EC2 start/stop policy"
  value       = aws_iam_policy.ec2_start_stop.arn
}

output "eventbridge_start_ec2_policy_arn" {
  description = "ARN of the EventBridge start EC2 policy"
  value       = aws_iam_policy.eventbridge_start_ec2.arn
}

output "eventbridge_stop_ec2_policy_arn" {
  description = "ARN of the EventBridge stop EC2 policy"
  value       = aws_iam_policy.eventbridge_stop_ec2.arn
}

output "cognito_publish_policy_arn" {
  description = "ARN of the Cognito publish policy"
  value       = aws_iam_policy.cognito_publish.arn
}

output "ecr_access_policy_arn" {
  description = "ARN of the ECR access policy"
  value       = aws_iam_policy.ecr_access.arn
}

output "kvs_access_policy_arn" {
  description = "ARN of the Kinesis Video Streams access policy"
  value       = aws_iam_policy.kvs_access.arn
}

#==============================================================================
# ROLE OUTPUTS
#==============================================================================

output "role_arns" {
  description = "Map of all role ARNs created by this module"
  value = {
    lambda_ec2_management = aws_iam_role.lambda_ec2_management.arn
    eventbridge_scheduler = aws_iam_role.eventbridge_scheduler.arn
    cognito_service      = aws_iam_role.cognito_service.arn
    ec2_application      = aws_iam_role.ec2_application.arn
  }
}

output "lambda_ec2_management_role_arn" {
  description = "ARN of the Lambda EC2 management role"
  value       = aws_iam_role.lambda_ec2_management.arn
}

output "lambda_ec2_management_role_name" {
  description = "Name of the Lambda EC2 management role"
  value       = aws_iam_role.lambda_ec2_management.name
}

output "eventbridge_scheduler_role_arn" {
  description = "ARN of the EventBridge scheduler role"
  value       = aws_iam_role.eventbridge_scheduler.arn
}

output "eventbridge_scheduler_role_name" {
  description = "Name of the EventBridge scheduler role"
  value       = aws_iam_role.eventbridge_scheduler.name
}

output "cognito_service_role_arn" {
  description = "ARN of the Cognito service role"
  value       = aws_iam_role.cognito_service.arn
}

output "cognito_service_role_name" {
  description = "Name of the Cognito service role"
  value       = aws_iam_role.cognito_service.name
}

output "ec2_application_role_arn" {
  description = "ARN of the EC2 application role"
  value       = aws_iam_role.ec2_application.arn
}

output "ec2_application_role_name" {
  description = "Name of the EC2 application role"
  value       = aws_iam_role.ec2_application.name
}

#==============================================================================
# INSTANCE PROFILE OUTPUTS
#==============================================================================

output "ec2_instance_profile_arn" {
  description = "ARN of the EC2 instance profile"
  value       = aws_iam_instance_profile.ec2_application.arn
}

output "ec2_instance_profile_name" {
  description = "Name of the EC2 instance profile"
  value       = aws_iam_instance_profile.ec2_application.name
}

#==============================================================================
# SUMMARY OUTPUTS
#==============================================================================

output "iam_resources_summary" {
  description = "Summary of all IAM resources created"
  value = {
    policies_count = 6
    roles_count    = 4
    policies = [
      aws_iam_policy.ec2_start_stop.name,
      aws_iam_policy.eventbridge_start_ec2.name,
      aws_iam_policy.eventbridge_stop_ec2.name,
      aws_iam_policy.cognito_publish.name,
      aws_iam_policy.ecr_access.name,
      aws_iam_policy.kvs_access.name
    ]
    roles = [
      aws_iam_role.lambda_ec2_management.name,
      aws_iam_role.eventbridge_scheduler.name,
      aws_iam_role.cognito_service.name,
      aws_iam_role.ec2_application.name
    ]
    instance_profiles = [
      aws_iam_instance_profile.ec2_application.name
    ]
  }
}
