# 🚀 Advanced Terraform & Terragrunt Patterns

## 🎯 Enterprise-Grade Patterns

### 1. **Multi-Account AWS Architecture**

#### Account Structure
```
Organization Root
├── Security Account (Logging, Audit)
├── Shared Services Account (DNS, Monitoring)
├── Development Account
├── Staging Account
└── Production Account
```

#### Cross-Account Role Assumption
```hcl
# terragrunt.hcl in each account
terraform {
  extra_arguments "assume_role" {
    commands = ["init", "plan", "apply", "destroy"]
    env_vars = {
      AWS_PROFILE = "cross-account-role"
    }
  }
}

# IAM role configuration
iam_role = "arn:aws:iam::${get_aws_account_id()}:role/TerraformExecutionRole"
```

### 2. **Advanced State Management**

#### State Isolation Strategy
```hcl
# Root terragrunt.hcl
remote_state {
  backend = "s3"
  config = {
    bucket = "terraform-state-${get_aws_account_id()}-${get_aws_region()}"
    key    = "${path_relative_to_include()}/terraform.tfstate"
    region = get_aws_region()
    
    # State isolation
    encrypt        = true
    dynamodb_table = "terraform-locks-${get_aws_account_id()}"
    
    # Versioning and lifecycle
    versioning = true
    
    # Access logging
    logging = {
      target_bucket = "terraform-state-access-logs-${get_aws_account_id()}"
      target_prefix = "state-access/"
    }
  }
}
```

#### State Backend Bootstrap
```hcl
# bootstrap/terragrunt.hcl
terraform {
  source = "../../modules/state-backend"
}

inputs = {
  state_bucket_name = "terraform-state-${get_aws_account_id()}-${get_aws_region()}"
  dynamodb_table_name = "terraform-locks-${get_aws_account_id()}"
  
  enable_versioning = true
  enable_encryption = true
  enable_access_logging = true
  
  tags = {
    Purpose = "Terraform State Management"
    Account = get_aws_account_id()
  }
}
```

### 3. **Complex Dependency Patterns**

#### Multi-Layer Dependencies
```hcl
# app/terragrunt.hcl
dependency "vpc" {
  config_path = "../networking/vpc"
  mock_outputs = {
    vpc_id = "vpc-mock"
    private_subnet_ids = ["subnet-mock-1", "subnet-mock-2"]
  }
}

dependency "security" {
  config_path = "../security/security-groups"
  mock_outputs = {
    app_security_group_id = "sg-mock"
  }
}

dependency "database" {
  config_path = "../data/rds"
  mock_outputs = {
    db_endpoint = "mock.rds.amazonaws.com"
    db_port = 5432
  }
}

dependency "secrets" {
  config_path = "../security/secrets"
  mock_outputs = {
    db_password_secret_arn = "arn:aws:secretsmanager:mock"
  }
}

inputs = {
  # Network configuration
  vpc_id     = dependency.vpc.outputs.vpc_id
  subnet_ids = dependency.vpc.outputs.private_subnet_ids
  
  # Security configuration
  security_group_ids = [dependency.security.outputs.app_security_group_id]
  
  # Database configuration
  database_endpoint = dependency.database.outputs.db_endpoint
  database_port     = dependency.database.outputs.db_port
  
  # Secrets configuration
  db_password_secret_arn = dependency.secrets.outputs.db_password_secret_arn
}
```

### 4. **Environment-Specific Configurations**

#### Dynamic Environment Configuration
```hcl
# environments/dev/env.hcl
locals {
  environment = "dev"
  
  # Environment-specific settings
  instance_types = {
    web = "t3.micro"
    app = "t3.small"
    db  = "db.t3.micro"
  }
  
  scaling = {
    min_size = 1
    max_size = 3
    desired_capacity = 1
  }
  
  # Feature flags
  features = {
    enable_monitoring = false
    enable_backup = false
    enable_multi_az = false
  }
}
```

```hcl
# environments/prod/env.hcl
locals {
  environment = "prod"
  
  instance_types = {
    web = "t3.large"
    app = "t3.xlarge"
    db  = "db.r5.xlarge"
  }
  
  scaling = {
    min_size = 2
    max_size = 20
    desired_capacity = 4
  }
  
  features = {
    enable_monitoring = true
    enable_backup = true
    enable_multi_az = true
  }
}
```

#### Using Environment Configuration
```hcl
# app/terragrunt.hcl
locals {
  env_vars = read_terragrunt_config(find_in_parent_folders("env.hcl"))
}

inputs = {
  environment = local.env_vars.locals.environment
  instance_type = local.env_vars.locals.instance_types.app
  
  min_size = local.env_vars.locals.scaling.min_size
  max_size = local.env_vars.locals.scaling.max_size
  desired_capacity = local.env_vars.locals.scaling.desired_capacity
  
  enable_monitoring = local.env_vars.locals.features.enable_monitoring
}
```

### 5. **Advanced Module Patterns**

#### Conditional Resource Creation
```hcl
# modules/app/main.tf
resource "aws_cloudwatch_log_group" "app" {
  count = var.enable_logging ? 1 : 0
  
  name              = "/aws/ecs/${var.app_name}"
  retention_in_days = var.log_retention_days
}

resource "aws_backup_vault" "app" {
  count = var.enable_backup ? 1 : 0
  
  name        = "${var.app_name}-backup-vault"
  kms_key_arn = var.backup_kms_key_arn
}

# Conditional outputs
output "log_group_name" {
  value = var.enable_logging ? aws_cloudwatch_log_group.app[0].name : null
}
```

#### Dynamic Block Patterns
```hcl
# Dynamic security group rules
resource "aws_security_group" "app" {
  name_prefix = "${var.app_name}-"
  vpc_id      = var.vpc_id
  
  dynamic "ingress" {
    for_each = var.ingress_rules
    content {
      from_port   = ingress.value.from_port
      to_port     = ingress.value.to_port
      protocol    = ingress.value.protocol
      cidr_blocks = ingress.value.cidr_blocks
      description = ingress.value.description
    }
  }
  
  dynamic "egress" {
    for_each = var.egress_rules
    content {
      from_port   = egress.value.from_port
      to_port     = egress.value.to_port
      protocol    = egress.value.protocol
      cidr_blocks = egress.value.cidr_blocks
      description = egress.value.description
    }
  }
}
```

### 6. **Secret Management Patterns**

#### Using AWS Secrets Manager
```hcl
# secrets/terragrunt.hcl
terraform {
  source = "../../modules/secrets"
}

inputs = {
  secrets = {
    database_password = {
      description = "Database master password"
      generate_secret_string = {
        length  = 32
        exclude_characters = "\"@/\\"
      }
    }
    
    api_key = {
      description = "External API key"
      secret_string = var.api_key  # From environment variable
    }
  }
}
```

#### SOPS Integration
```hcl
# Using SOPS for encrypted secrets
locals {
  secrets = yamldecode(sops_decrypt_file("${get_terragrunt_dir()}/secrets.yaml"))
}

inputs = {
  database_password = local.secrets.database.password
  api_keys = local.secrets.api_keys
}
```

### 7. **Provider Configuration Patterns**

#### Multi-Region Deployment
```hcl
# Root terragrunt.hcl
generate "providers" {
  path      = "providers.tf"
  if_exists = "overwrite"
  contents = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Primary region provider
provider "aws" {
  region = var.primary_region
  
  default_tags {
    tags = var.default_tags
  }
}

# Secondary region provider for DR
provider "aws" {
  alias  = "dr"
  region = var.dr_region
  
  default_tags {
    tags = merge(var.default_tags, {
      Region = "DR"
    })
  }
}
EOF
}
```

### 8. **Testing Patterns**

#### Terratest Integration
```go
// test/terragrunt_test.go
func TestTerragruntVPC(t *testing.T) {
    terragruntOptions := &terragrunt.Options{
        TerragruntConfigPath: "../environments/test/vpc",
        NoAutoInit:          true,
    }
    
    defer terragrunt.TgDestroy(t, terragruntOptions)
    terragrunt.TgApply(t, terragruntOptions)
    
    // Validate outputs
    vpcId := terragrunt.TgOutput(t, terragruntOptions, "vpc_id")
    assert.NotEmpty(t, vpcId)
}
```

### 9. **CI/CD Integration Patterns**

#### GitLab CI Pipeline
```yaml
# .gitlab-ci.yml
stages:
  - validate
  - plan
  - apply

variables:
  TF_ROOT: ${CI_PROJECT_DIR}
  TF_IN_AUTOMATION: "true"

.terragrunt_template: &terragrunt_template
  image: alpine/terragrunt:latest
  before_script:
    - cd $TF_ROOT/$ENVIRONMENT

validate:
  <<: *terragrunt_template
  stage: validate
  script:
    - terragrunt run-all validate
    - terragrunt run-all fmt -check

plan:
  <<: *terragrunt_template
  stage: plan
  script:
    - terragrunt run-all plan -out=tfplan
  artifacts:
    paths:
      - "**/*.tfplan"

apply:
  <<: *terragrunt_template
  stage: apply
  script:
    - terragrunt run-all apply tfplan
  when: manual
  only:
    - main
```

### 10. **Performance Optimization**

#### Provider Caching
```hcl
# Root terragrunt.hcl
terraform {
  extra_arguments "provider_cache" {
    commands = ["init"]
    env_vars = {
      TF_PLUGIN_CACHE_DIR = "${get_repo_root()}/.terraform.d/plugin-cache"
    }
  }
}
```

#### Parallel Execution
```bash
# Run with increased parallelism
terragrunt run-all apply --terragrunt-parallelism 10

# Use dependency optimization
terragrunt run-all apply --terragrunt-ignore-dependency-errors
```

## 🎯 Best Practices Summary

1. **State Management**: Use separate state files per environment and service
2. **Dependencies**: Minimize cross-dependencies, use mock outputs
3. **Secrets**: Never commit secrets, use proper secret management
4. **Testing**: Implement automated testing with Terratest
5. **CI/CD**: Use proper approval workflows for production
6. **Monitoring**: Implement proper logging and monitoring
7. **Documentation**: Document all patterns and decisions

## 🔗 Next Steps
- **05-real-world-projects**: Complete production examples
- **06-best-practices**: Industry standards and guidelines
