# 🏗️ Terraform Fundamentals

## 📚 Core Concepts Mastery

### 1. Infrastructure as Code (IaC) Principles
- **Declarative vs Imperative**: Understanding the Terraform approach
- **Idempotency**: Same configuration = Same result
- **Version Control**: Infrastructure versioning and collaboration
- **State Management**: The heart of Terraform operations

### 2. Terraform Workflow
```bash
# The Essential Terraform Commands
terraform init      # Initialize working directory
terraform plan       # Preview changes
terraform apply      # Apply changes
terraform destroy    # Destroy infrastructure
terraform validate   # Validate configuration
terraform fmt        # Format code
terraform show       # Show current state
terraform output     # Display outputs
```

### 3. Configuration Language (HCL)
- **Blocks**: resource, data, variable, output, locals, module
- **Arguments**: Key-value pairs within blocks
- **Expressions**: References, functions, conditionals
- **Meta-arguments**: count, for_each, depends_on, lifecycle

### 4. Resource Lifecycle
```hcl
resource "aws_instance" "example" {
  ami           = "ami-0c02fb55956c7d316"
  instance_type = "t3.micro"
  
  lifecycle {
    create_before_destroy = true
    prevent_destroy       = true
    ignore_changes       = [tags]
  }
}
```

### 5. State Management Deep Dive
- **Local State**: terraform.tfstate file
- **Remote State**: S3, Terraform Cloud, Consul
- **State Locking**: Preventing concurrent modifications
- **State Import**: Bringing existing resources under management

### 6. Variables and Outputs
```hcl
# Variables
variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "t3.micro"
  validation {
    condition     = contains(["t3.micro", "t3.small"], var.instance_type)
    error_message = "Instance type must be t3.micro or t3.small."
  }
}

# Outputs
output "instance_ip" {
  description = "Public IP of the instance"
  value       = aws_instance.example.public_ip
  sensitive   = false
}
```

### 7. Data Sources
```hcl
# Fetch existing AWS resources
data "aws_ami" "ubuntu" {
  most_recent = true
  owners      = ["099720109477"] # Canonical
  
  filter {
    name   = "name"
    values = ["ubuntu/images/hvm-ssd/ubuntu-focal-20.04-amd64-server-*"]
  }
}
```

### 8. Functions and Expressions
- **String Functions**: format, join, split, regex
- **Collection Functions**: length, keys, values, lookup
- **Encoding Functions**: base64encode, jsonencode, yamlencode
- **Filesystem Functions**: file, templatefile, fileexists
- **Date/Time Functions**: timestamp, formatdate

### 9. Conditional Logic
```hcl
# Conditional expressions
instance_type = var.environment == "prod" ? "t3.large" : "t3.micro"

# Count with conditions
count = var.create_instance ? 1 : 0

# For expressions
instance_tags = {
  for k, v in var.tags : k => v
  if k != "temporary"
}
```

### 10. Modules
```hcl
module "vpc" {
  source = "./modules/vpc"
  
  cidr_block = "10.0.0.0/16"
  name       = "my-vpc"
  
  tags = {
    Environment = "dev"
  }
}
```

## 🎯 Hands-On Exercises

1. **Basic Resource Creation**
2. **State Management Practice**
3. **Variable and Output Usage**
4. **Data Source Integration**
5. **Module Development**
6. **Conditional Logic Implementation**

## 📝 Best Practices Checklist

- [ ] Use consistent naming conventions
- [ ] Implement proper tagging strategy
- [ ] Use remote state with locking
- [ ] Validate configurations before apply
- [ ] Use modules for reusability
- [ ] Document your infrastructure
- [ ] Implement proper secret management
- [ ] Use version constraints for providers

## 🔗 Next Steps

After mastering these fundamentals, proceed to:
- **02-aws-provider-deep-dive**: AWS-specific resources and patterns
- **03-terragrunt-fundamentals**: DRY configurations and orchestration
